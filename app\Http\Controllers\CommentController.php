<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Mail\CommentNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class CommentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Comment::with(['post', 'allReplies']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('comment', 'like', '%' . $request->search . '%')
                  ->orWhereHas('post', function($postQuery) use ($request) {
                      $postQuery->where('title', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Filter by approval status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_approved', $request->status);
        }

        // Only show parent comments (not replies) in the main list
        $comments = $query->whereNull('parent_id')->latest()->paginate(15);
        $statistics = Comment::getStatistics();

        return view('dashboard.comments.index', compact('comments', 'statistics'));
    }

    /**
     * Approve a comment.
     */
    public function approve(Request $request, Comment $comment)
    {
        $comment->update(['is_approved' => true]);

        // Send approval notification to comment author
        try {
            Mail::to($comment->email)->send(new CommentNotification($comment, 'approved'));
        } catch (\Exception $e) {
            // Log error but don't fail the approval
            \Log::error('Failed to send comment approval email: ' . $e->getMessage());
        }

        // Handle AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment approved successfully!'
            ]);
        }

        return redirect()->route('comments.index')
            ->with('success', 'Comment approved successfully!');
    }

    /**
     * Reject a comment.
     */
    public function reject(Request $request, Comment $comment)
    {
        $comment->update(['is_approved' => false]);

        // Handle AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment rejected successfully!'
            ]);
        }

        return redirect()->route('comments.index')
            ->with('success', 'Comment rejected successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Store a reply to a comment.
     */
    public function reply(Request $request, Comment $comment)
    {
        try {
            // Debug logging
            \Log::info('Reply attempt', [
                'comment_id' => $comment->id,
                'user_id' => auth()->id(),
                'reply_content' => $request->reply_content,
                'request_data' => $request->all()
            ]);

            // Validate the request
            $validated = $request->validate([
                'reply_content' => 'required|string|min:10|max:1000',
            ]);

            // Create the reply
            $reply = Comment::create([
                'post_id' => $comment->post_id,
                'parent_id' => $comment->id,
                'name' => auth()->user()->name,
                'email' => auth()->user()->email,
                'comment' => $validated['reply_content'],
                'is_approved' => true, // Admin replies are auto-approved
                'ip_address' => $request->ip(),
            ]);

            \Log::info('Reply created successfully', [
                'reply_id' => $reply->id,
                'parent_comment_id' => $comment->id
            ]);

        // Send reply notification to original comment author
        try {
            if ($comment->email !== auth()->user()->email) {
                Mail::to($comment->email)->send(new CommentNotification($reply, 'reply'));
            }

            // Also notify admin about the reply
            Mail::to('<EMAIL>')->send(new CommentNotification($reply, 'new'));
        } catch (\Exception $e) {
            // Log error but don't fail the reply
            \Log::error('Failed to send reply notification email: ' . $e->getMessage());
        }

            // Handle AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Reply posted successfully!',
                    'reply' => $reply
                ]);
            }

            return redirect()->route('comments.index')
                ->with('success', 'Reply posted successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Reply validation failed', [
                'errors' => $e->errors(),
                'comment_id' => $comment->id
            ]);
            return redirect()->route('comments.index')
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            \Log::error('Reply creation failed', [
                'error' => $e->getMessage(),
                'comment_id' => $comment->id,
                'user_id' => auth()->id()
            ]);
            return redirect()->route('comments.index')
                ->with('error', 'Failed to post reply. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  Comment  $comment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Comment $comment)
    {
        // Delete all replies first
        $comment->allReplies()->delete();

        // Delete the comment
        $comment->delete();

        return redirect()->route('comments.index')
            ->with('success', 'Comment and all its replies deleted successfully!');
    }
}
