<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'status',
        'description',
        'image'
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    // Relationship with posts
    public function posts()
    {
        return $this->hasMany(Post::class);
    }
}
