/**
 * AJAX Search Functionality
 * Provides real-time search across the application
 */

class AjaxSearch {
    constructor() {
        this.searchTimeout = null;
        this.currentRequest = null;
        this.init();
    }

    init() {
        this.initWebsiteSearch();
        this.initDashboardSearch();
    }

    /**
     * Initialize website search functionality
     */
    initWebsiteSearch() {
        const searchInputs = document.querySelectorAll('.website-search');
        
        searchInputs.forEach(input => {
            const container = this.createSearchContainer(input);
            
            input.addEventListener('input', (e) => {
                this.handleSearch(e.target.value, '/search/posts', container, 'website');
            });

            input.addEventListener('focus', (e) => {
                if (e.target.value.length >= 2) {
                    container.style.display = 'block';
                }
            });

            // Hide results when clicking outside
            document.addEventListener('click', (e) => {
                if (!input.contains(e.target) && !container.contains(e.target)) {
                    container.style.display = 'none';
                }
            });
        });
    }

    /**
     * Initialize dashboard search functionality
     */
    initDashboardSearch() {
        // Posts search
        const postsSearch = document.querySelector('#posts-search');
        if (postsSearch) {
            const container = this.createSearchContainer(postsSearch);
            
            postsSearch.addEventListener('input', (e) => {
                this.handleSearch(e.target.value, '/search/dashboard-posts', container, 'dashboard-posts');
            });
        }

        // Users search
        const usersSearch = document.querySelector('#users-search');
        if (usersSearch) {
            const container = this.createSearchContainer(usersSearch);
            
            usersSearch.addEventListener('input', (e) => {
                this.handleSearch(e.target.value, '/search/users', container, 'dashboard-users');
            });
        }
    }

    /**
     * Create search results container
     */
    createSearchContainer(input) {
        let container = input.parentNode.querySelector('.search-results');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'search-results';
            container.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #ddd;
                border-top: none;
                max-height: 400px;
                overflow-y: auto;
                z-index: 1000;
                display: none;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            `;
            
            // Make parent position relative
            input.parentNode.style.position = 'relative';
            input.parentNode.appendChild(container);
        }
        
        return container;
    }

    /**
     * Handle search input
     */
    handleSearch(query, url, container, type) {
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Cancel previous request
        if (this.currentRequest) {
            this.currentRequest.abort();
        }

        if (query.length < 2) {
            container.style.display = 'none';
            return;
        }

        // Show loading
        container.innerHTML = '<div class="search-loading">Searching...</div>';
        container.style.display = 'block';

        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query, url, container, type);
        }, 300);
    }

    /**
     * Perform AJAX search
     */
    performSearch(query, url, container, type) {
        this.currentRequest = fetch(`${url}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                this.displayResults(data, container, type);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    container.innerHTML = '<div class="search-error">Search failed. Please try again.</div>';
                }
            });
    }

    /**
     * Display search results
     */
    displayResults(results, container, type) {
        if (results.length === 0) {
            container.innerHTML = '<div class="search-no-results">No results found</div>';
            return;
        }

        let html = '';
        
        results.forEach(item => {
            switch (type) {
                case 'website':
                    html += this.createWebsiteResultItem(item);
                    break;
                case 'dashboard-posts':
                    html += this.createDashboardPostItem(item);
                    break;
                case 'dashboard-users':
                    html += this.createDashboardUserItem(item);
                    break;
            }
        });

        container.innerHTML = html;
    }

    /**
     * Create website search result item
     */
    createWebsiteResultItem(item) {
        return `
            <div class="search-result-item" onclick="window.location.href='${item.url}'">
                <div class="search-item-content">
                    ${item.image ? `<img src="${item.image}" alt="${item.title}" class="search-item-image">` : ''}
                    <div class="search-item-details">
                        <h6 class="search-item-title">${item.title}</h6>
                        <p class="search-item-excerpt">${item.excerpt}</p>
                        <small class="search-item-meta">
                            <span class="category">${item.category}</span> • 
                            <span class="author">${item.author}</span> • 
                            <span class="date">${item.date}</span>
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create dashboard post result item
     */
    createDashboardPostItem(item) {
        return `
            <div class="search-result-item" onclick="window.location.href='${item.url}'">
                <div class="search-item-content">
                    ${item.image ? `<img src="${item.image}" alt="${item.title}" class="search-item-image">` : ''}
                    <div class="search-item-details">
                        <h6 class="search-item-title">${item.title}</h6>
                        <p class="search-item-excerpt">${item.excerpt}</p>
                        <small class="search-item-meta">
                            <span class="category">${item.category}</span> • 
                            <span class="status">${item.status}</span> • 
                            <span class="date">${item.date}</span>
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create dashboard user result item
     */
    createDashboardUserItem(item) {
        return `
            <div class="search-result-item" onclick="window.location.href='${item.url}'">
                <div class="search-item-content">
                    <img src="${item.image}" alt="${item.name}" class="search-item-avatar">
                    <div class="search-item-details">
                        <h6 class="search-item-title">${item.name}</h6>
                        <p class="search-item-excerpt">${item.email}</p>
                        <small class="search-item-meta">
                            <span class="role">${item.role}</span> • 
                            <span class="date">Joined ${item.joined}</span>
                        </small>
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new AjaxSearch();
});
