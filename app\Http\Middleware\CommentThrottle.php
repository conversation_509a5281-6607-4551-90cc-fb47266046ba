<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class CommentThrottle
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $key = $this->resolveRequestSignature($request);

        // Allow 3 comments per minute per IP
        $maxAttempts = 3;
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => "Too many comments. Please wait {$seconds} seconds before commenting again.",
                    'retry_after' => $seconds
                ], 429);
            }

            return redirect()->back()->with('error', "Too many comments. Please wait {$seconds} seconds before commenting again.");
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        // Additional spam protection
        $this->checkForSpam($request);

        return $next($request);
    }

    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request): string
    {
        return sha1(
            $request->method() .
            '|' . $request->server('SERVER_NAME') .
            '|' . $request->path() .
            '|' . $request->ip()
        );
    }

    /**
     * Basic spam protection
     */
    protected function checkForSpam(Request $request): void
    {
        $comment = $request->input('comment', '');

        // Check for suspicious patterns
        $spamPatterns = [
            '/https?:\/\/[^\s]+/i', // URLs
            '/\b(buy|sale|discount|offer|deal|free|win|prize)\b/i', // Spam keywords
            '/(.)\1{4,}/', // Repeated characters
        ];

        foreach ($spamPatterns as $pattern) {
            if (preg_match($pattern, $comment)) {
                // Log suspicious activity
                \Log::warning('Potential spam comment detected', [
                    'ip' => $request->ip(),
                    'comment' => $comment,
                    'pattern' => $pattern
                ]);

                // You could also block the comment here
                // throw new \Exception('Comment appears to be spam');
                break;
            }
        }

        // Check comment length (too short or too long might be spam)
        if (strlen($comment) < 5 || strlen($comment) > 2000) {
            \Log::warning('Suspicious comment length', [
                'ip' => $request->ip(),
                'length' => strlen($comment)
            ]);
        }
    }
}
