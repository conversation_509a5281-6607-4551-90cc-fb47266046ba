@extends('theme.layout.master')

@section('title', 'View Tag')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-eye"></i> View Tag</h1>
    <p>
      <span class="badge" style="background-color: {{ $tag->color }}">{{ $tag->name }}</span>
    </p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('tags.index') }}">Tags</a></li>
    <li class="breadcrumb-item active">View</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <!-- Tag Information -->
    <div class="tile mb-4">
      <div class="tile-title-w-btn">
        <h3 class="title">Tag Information</h3>
      </div>
      <div class="tile-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td><strong>Name:</strong></td>
                <td>
                  <span class="badge" style="background-color: {{ $tag->color }}">{{ $tag->name }}</span>
                </td>
              </tr>
              <tr>
                <td><strong>Slug:</strong></td>
                <td><code>{{ $tag->slug }}</code></td>
              </tr>
              <tr>
                <td><strong>Color:</strong></td>
                <td>
                  <div class="d-flex align-items-center">
                    <div class="color-preview me-2" 
                         style="width: 30px; height: 30px; background-color: {{ $tag->color }}; border-radius: 5px; border: 1px solid #ddd;"></div>
                    <code>{{ $tag->color }}</code>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td><strong>Posts Count:</strong></td>
                <td><span class="badge bg-info">{{ $tag->posts->count() }}</span></td>
              </tr>
              <tr>
                <td><strong>Created:</strong></td>
                <td>{{ $tag->created_at->format('M d, Y H:i') }}</td>
              </tr>
              <tr>
                <td><strong>Updated:</strong></td>
                <td>{{ $tag->updated_at->format('M d, Y H:i') }}</td>
              </tr>
            </table>
          </div>
        </div>

        @if($tag->description)
        <div class="mt-3">
          <strong>Description:</strong>
          <p class="text-muted mt-2">{{ $tag->description }}</p>
        </div>
        @endif
      </div>
    </div>

    <!-- Associated Posts -->
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">Posts with this Tag ({{ $tag->posts->count() }})</h3>
      </div>
      <div class="tile-body">
        @if($tag->posts->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Category</th>
                  <th>Author</th>
                  <th>Status</th>
                  <th>Published</th>
                  <th>Views</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                @foreach($tag->posts as $post)
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      @if($post->featured_image)
                        <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}" 
                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                      @endif
                      <div>
                        <strong>{{ Str::limit($post->title, 40) }}</strong>
                        @if($post->excerpt)
                          <br><small class="text-muted">{{ Str::limit($post->excerpt, 60) }}</small>
                        @endif
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-secondary">{{ $post->category->name }}</span>
                  </td>
                  <td>{{ $post->user->name }}</td>
                  <td>
                    @if($post->status)
                      <span class="badge bg-success">Published</span>
                    @else
                      <span class="badge bg-warning">Draft</span>
                    @endif
                  </td>
                  <td>
                    @if($post->published_at)
                      {{ $post->published_at->format('M d, Y') }}
                    @else
                      <span class="text-muted">Not published</span>
                    @endif
                  </td>
                  <td>
                    <span class="badge bg-info">{{ $post->views_count }}</span>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <a href="{{ route('posts.show', $post) }}" class="btn btn-sm btn-info" title="View">
                        <i class="bi bi-eye"></i>
                      </a>
                      <a href="{{ route('posts.edit', $post) }}" class="btn btn-sm btn-warning" title="Edit">
                        <i class="bi bi-pencil"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        @else
          <div class="text-center py-4">
            <div class="text-muted">
              <i class="bi bi-file-text fs-1"></i>
              <p class="mt-2">No posts are using this tag yet.</p>
              <a href="{{ route('posts.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create New Post
              </a>
            </div>
          </div>
        @endif
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <!-- Actions -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Actions</h3>
      </div>
      <div class="tile-body">
        <div class="d-grid gap-2">
          <a href="{{ route('tags.edit', $tag) }}" class="btn btn-warning">
            <i class="bi bi-pencil"></i> Edit Tag
          </a>
          <a href="{{ route('tags.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Tags
          </a>
          <form action="{{ route('tags.destroy', $tag) }}" method="POST" 
                onsubmit="return confirm('Are you sure you want to delete this tag? This will remove it from all associated posts.')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger w-100">
              <i class="bi bi-trash"></i> Delete Tag
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Tag Usage Statistics -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Usage Statistics</h3>
      </div>
      <div class="tile-body">
        <div class="row text-center">
          <div class="col-6">
            <div class="widget-small primary coloured-icon">
              <i class="icon bi bi-file-text fs-3"></i>
              <div class="info">
                <h6>Total Posts</h6>
                <p><b>{{ $tag->posts->count() }}</b></p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="widget-small info coloured-icon">
              <i class="icon bi bi-eye fs-3"></i>
              <div class="info">
                <h6>Total Views</h6>
                <p><b>{{ $tag->posts->sum('views_count') }}</b></p>
              </div>
            </div>
          </div>
        </div>
        <div class="row text-center mt-2">
          <div class="col-6">
            <div class="widget-small success coloured-icon">
              <i class="icon bi bi-check-circle fs-3"></i>
              <div class="info">
                <h6>Published</h6>
                <p><b>{{ $tag->posts->where('status', true)->count() }}</b></p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="widget-small warning coloured-icon">
              <i class="icon bi bi-clock fs-3"></i>
              <div class="info">
                <h6>Drafts</h6>
                <p><b>{{ $tag->posts->where('status', false)->count() }}</b></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tag Preview -->
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">Tag Preview</h3>
      </div>
      <div class="tile-body">
        <p>This is how the tag appears on your blog:</p>
        <div class="p-3 border rounded bg-light text-center">
          <span class="badge fs-6" style="background-color: {{ $tag->color }}">
            {{ $tag->name }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
