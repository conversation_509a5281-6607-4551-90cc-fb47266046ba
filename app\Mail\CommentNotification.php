<?php

namespace App\Mail;

use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CommentNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $comment;
    public $type;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Comment $comment, $type = 'new')
    {
        $this->comment = $comment;
        $this->type = $type; // 'new', 'reply', 'approved'
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        $subjects = [
            'new' => 'New Comment on Your Blog',
            'reply' => 'New Reply to Your Comment',
            'approved' => 'Your Comment Has Been Approved'
        ];

        return new Envelope(
            subject: $subjects[$this->type] ?? 'Comment Notification',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.comment-notification',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
