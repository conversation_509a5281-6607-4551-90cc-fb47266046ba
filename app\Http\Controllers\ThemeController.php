<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Category;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Request;

class ThemeController extends Controller
{
    public function home()
    {
        // Get dashboard statistics
        $stats = [
            'total_posts' => Post::count(),
            'published_posts' => Post::where('status', true)->count(),
            'draft_posts' => Post::where('status', false)->count(),
            'total_categories' => Category::count(),
            'active_categories' => Category::where('status', true)->count(),
            'total_tags' => Tag::count(),
            'total_users' => User::count(),
            'total_views' => Post::sum('views_count'),
        ];

        // Get recent posts
        $recent_posts = Post::with(['category', 'user'])
            ->latest('created_at')
            ->limit(5)
            ->get();

        // Get popular posts (by views)
        $popular_posts = Post::with(['category', 'user'])
            ->where('status', true)
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Get monthly post statistics for chart
        $monthly_posts = Post::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // Fill missing months with 0
        $monthly_data = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthly_data[] = $monthly_posts[$i] ?? 0;
        }

        return view('dashboard.index', compact('stats', 'recent_posts', 'popular_posts', 'monthly_data'));
    }
}
