@extends('theme.layout.master')

@section('title', 'Users Management')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-people"></i> Users Management</h1>
    <p>Manage system users</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Users</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="tile-title">All Users</h3>
          <a href="{{ route('users.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add New User
          </a>
        </div>

        <!-- Search and Filter Section -->
        <form method="GET" action="{{ route('users.index') }}" class="mb-4">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group dashboard-search-container">
                <input type="text" id="users-search" class="form-control dashboard-search" placeholder="Search users by name or email..." autocomplete="off">
                <input type="text" name="search" class="form-control" placeholder="Search users by name or email..."
                       value="{{ request('search') }}" style="display: none;">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <select name="role" class="form-control">
                  <option value="">All Roles</option>
                  <option value="1" {{ request('role') === '1' ? 'selected' : '' }}>Administrator</option>
                  <option value="0" {{ request('role') === '0' ? 'selected' : '' }}>User</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> Search
              </button>
              @if(request()->hasAny(['search', 'role']))
                <a href="{{ route('users.index') }}" class="btn btn-secondary ms-1">
                  <i class="bi bi-x-circle"></i> Clear
                </a>
              @endif
            </div>
          </div>
        </form>

        @if(session('success'))
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        @endif

        @if(session('error'))
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        @endif

        <div class="table-responsive">
          <table class="table table-hover table-bordered" id="usersTable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Posts Count</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @forelse($users as $user)
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    <img src="{{ $user->profile_image_url }}"
                         alt="{{ $user->name }}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                    <div>
                      <strong>{{ $user->name }}</strong>
                    </div>
                  </div>
                </td>
                <td>{{ $user->email }}</td>
                <td>
                  @if($user->is_admin)
                    <span class="badge bg-danger">Administrator</span>
                  @else
                    <span class="badge bg-secondary">User</span>
                  @endif
                </td>
                <td>
                  <span class="badge bg-info">{{ $user->posts_count }}</span>
                </td>
                <td>
                  {{ $user->created_at->format('M d, Y') }}
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('users.show', $user) }}" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="{{ route('users.edit', $user) }}" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                    @if(!$user->is_admin || \App\Models\User::where('is_admin', true)->count() > 1)
                    <form action="{{ route('users.destroy', $user) }}" method="POST" class="d-inline" 
                          onsubmit="return confirm('Are you sure you want to delete this user?')">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                    @endif
                  </div>
                </td>
              </tr>
              @empty
              <tr>
                <td colspan="6" class="text-center py-4">
                  <div class="text-muted">
                    <i class="bi bi-people fs-1"></i>
                    <p class="mt-2">No users found.</p>
                  </div>
                </td>
              </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        @if($users->hasPages())
          <div class="d-flex justify-content-center mt-3">
            {{ $users->links() }}
          </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- User Statistics -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="widget-small primary coloured-icon">
      <i class="icon bi bi-people fs-1"></i>
      <div class="info">
        <h4>Total Users</h4>
        <p><b>{{ $users->total() }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small danger coloured-icon">
      <i class="icon bi bi-shield-check fs-1"></i>
      <div class="info">
        <h4>Administrators</h4>
        <p><b>{{ $users->where('is_admin', true)->count() }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small success coloured-icon">
      <i class="icon bi bi-person fs-1"></i>
      <div class="info">
        <h4>Regular Users</h4>
        <p><b>{{ $users->where('is_admin', false)->count() }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small info coloured-icon">
      <i class="icon bi bi-file-text fs-1"></i>
      <div class="info">
        <h4>Total Posts</h4>
        <p><b>{{ $users->sum('posts_count') }}</b></p>
      </div>
    </div>
  </div>
</div>
@endsection
