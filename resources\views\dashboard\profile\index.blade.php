@extends('theme.layout.master')

@section('title', 'My Profile')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-person-circle"></i> My Profile</h1>
    <p>Manage your profile information and account settings</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Profile</li>
  </ul>
</div>

<div class="row">
  <!-- Profile Stats -->
  <div class="col-md-12 mb-4">
    <div class="row">
      <div class="col-md-3 col-sm-6">
        <div class="widget-small primary coloured-icon">
          <i class="icon bi bi-file-text fs-1"></i>
          <div class="info">
            <h4>{{ $stats['posts_count'] }}</h4>
            <p><b>Total Posts</b></p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="widget-small info coloured-icon">
          <i class="icon bi bi-check-circle fs-1"></i>
          <div class="info">
            <h4>{{ $stats['published_posts'] }}</h4>
            <p><b>Published</b></p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="widget-small warning coloured-icon">
          <i class="icon bi bi-pencil fs-1"></i>
          <div class="info">
            <h4>{{ $stats['draft_posts'] }}</h4>
            <p><b>Drafts</b></p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="widget-small danger coloured-icon">
          <i class="icon bi bi-eye fs-1"></i>
          <div class="info">
            <h4>{{ number_format($stats['total_views']) }}</h4>
            <p><b>Total Views</b></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Profile Information -->
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-person-fill"></i> Profile Information
        </h3>
      </div>
      <div class="tile-body">
        <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
          @csrf
          @method('PUT')
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="profile_image" class="form-label">Profile Image</label>
            <input type="file" class="form-control @error('profile_image') is-invalid @enderror" 
                   id="profile_image" name="profile_image" accept="image/*">
            @error('profile_image')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <small class="form-text text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
          </div>

          <div class="mb-3">
            <label class="form-label">Account Type</label>
            <div class="form-control-plaintext">
              @if($user->is_admin)
                <span class="badge bg-danger">
                  <i class="bi bi-shield-check"></i> Administrator
                </span>
              @else
                <span class="badge bg-primary">
                  <i class="bi bi-person"></i> User
                </span>
              @endif
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Member Since</label>
            <div class="form-control-plaintext">
              {{ $user->created_at->format('F d, Y') }}
            </div>
          </div>

          <div class="tile-footer">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-check-lg"></i> Update Profile
            </button>
            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
              <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Profile Image Preview -->
  <div class="col-md-4">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-image"></i> Profile Picture
        </h3>
      </div>
      <div class="tile-body text-center">
        <div class="profile-image-container mb-3">
          <img src="{{ $user->profile_image_url }}" alt="{{ $user->name }}" 
               class="profile-image img-fluid rounded-circle" 
               style="width: 200px; height: 200px; object-fit: cover; border: 4px solid #007bff;">
        </div>
        
        <div class="profile-info">
          <h4 class="mb-2">{{ $user->name }}</h4>
          <p class="text-muted mb-3">{{ $user->email }}</p>
          
          @if($user->profile_image)
            <form action="{{ route('profile.remove-image') }}" method="POST" class="d-inline">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-outline-danger btn-sm" 
                      onclick="return confirm('Are you sure you want to remove your profile image?')">
                <i class="bi bi-trash"></i> Remove Image
              </button>
            </form>
          @endif
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-lightning"></i> Quick Actions
        </h3>
      </div>
      <div class="tile-body">
        <div class="d-grid gap-2">
          <a href="{{ route('posts.create') }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Create New Post
          </a>
          <a href="{{ route('posts.index') }}" class="btn btn-primary">
            <i class="bi bi-file-text"></i> Manage Posts
          </a>
          <a href="{{ route('settings.index') }}" class="btn btn-warning">
            <i class="bi bi-gear"></i> Account Settings
          </a>
          <a href="{{ route('comments.index') }}" class="btn btn-info">
            <i class="bi bi-chat-dots"></i> Manage Comments
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.profile-image-container {
  position: relative;
  display: inline-block;
}

.profile-image {
  transition: all 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.widget-small {
  transition: all 0.3s ease;
}

.widget-small:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.tile {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.tile:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}
</style>

@push('js')
<script>
  // Preview profile image before upload
  document.getElementById('profile_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        document.querySelector('.profile-image').src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  });
</script>
@endpush
@endsection
