<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Vali is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.">
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@pratikborsadiya">
  <meta property="twitter:creator" content="@pratikborsadiya">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Vali Admin">
  <meta property="og:title" content="Vali - Free Bootstrap 5 admin theme">
  <meta property="og:url" content="http://pratikborsadiya.in/blog/vali-admin">
  <meta property="og:image" content="http://pratikborsadiya.in/blog/vali-admin/hero-social.png">
  <meta property="og:description" content="Vali is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.">
  <title>Blog Management - <?php echo $__env->yieldContent('title', 'Dashboard'); ?></title>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Main CSS-->
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/main.css')); ?>">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
  <!-- Custom Dashboard CSS - Minimal, non-interfering version -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/dashboard/custom.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('css/ajax-search.css')); ?>">

  <!-- CKEditor 4 Custom Styling -->
  <style>
    /* CKEditor 4 container styling */
    .cke {
      border: 1px solid #ced4da !important;
      border-radius: 8px !important;
      overflow: hidden;
    }

    .cke_top {
      background: #f8f9fa !important;
      border-bottom: 1px solid #e9ecef !important;
      padding: 8px !important;
    }

    .cke_bottom {
      background: #f8f9fa !important;
      border-top: 1px solid #e9ecef !important;
    }

    .cke_contents {
      background: white !important;
    }

    .cke_wysiwyg_frame {
      background: white !important;
    }

    /* Toolbar button styling */
    .cke_toolbar {
      background: transparent !important;
      border: none !important;
      margin: 2px 0 !important;
    }

    .cke_button {
      background: transparent !important;
      border: 1px solid transparent !important;
      border-radius: 4px !important;
      margin: 1px !important;
    }

    .cke_button:hover {
      background: #e9ecef !important;
      border-color: #ced4da !important;
    }

    .cke_button_on {
      background: #007bff !important;
      border-color: #0056b3 !important;
    }

    .cke_button_on .cke_button_icon {
      filter: brightness(0) invert(1);
    }

    /* Focus state */
    .cke_focus {
      border-color: #86b7fe !important;
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .cke_toolbar {
        text-align: center;
      }

      .cke_toolgroup {
        margin: 2px 1px !important;
      }
    }

    /* Hide original textarea when CKEditor is active */
    .cke_editable {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
      font-size: 14px !important;
      line-height: 1.6 !important;
      padding: 20px !important;
    }
  </style>
</head>
<body class="app sidebar-mini">

  
  <?php echo $__env->make('dashboard.template.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  
  <?php echo $__env->make('dashboard.template.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  
  <main class="app-content">
    <?php echo $__env->yieldContent('content'); ?>
  </main>

  
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Original main.js MUST load first for core functionality -->
  <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <!-- DataTables -->
  <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
  <!-- Select2 CSS (needs to be in head) -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

  <!-- CKEditor 4.22.1 - Free Version -->
  <script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"></script>
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <!-- Custom Dashboard JavaScript - loads after core functionality -->
  <script src="<?php echo e(asset('js/dashboard/main.js')); ?>"></script>
  <script src="<?php echo e(asset('js/ajax-search.js')); ?>"></script>

  
  <?php echo $__env->yieldPushContent('js'); ?>

</body>
</html>
<?php /**PATH C:\xampp\htdocs\blog-website\resources\views/theme/layout/master.blade.php ENDPATH**/ ?>