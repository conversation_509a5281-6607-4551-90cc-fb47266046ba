
<?php $__env->startSection('content'); ?>
   <!-- Page Content -->
    <!-- Banner Starts Here -->
    <div class="heading-page header-text">
      <section class="page-heading">
        <div class="container">
          <div class="row">
            <div class="col-lg-12">
              <div class="text-content">
                <h4><?php echo e($post->category->name); ?></h4>
                <h2><?php echo e($post->title); ?></h2>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a rel="nofollow" href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts grid-system">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                <div class="col-lg-12">
                  <div class="blog-post">
                    <?php if($post->featured_image): ?>
                    <div class="blog-thumb">
                      <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>">
                    </div>
                    <?php endif; ?>
                    <div class="down-content">
                      <span><?php echo e($post->category->name); ?></span>
                      <h4><?php echo e($post->title); ?></h4>
                      <ul class="post-info">
                        <li><a href="#"><?php echo e($post->user->name); ?></a></li>
                        <li><a href="#"><?php echo e($post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y')); ?></a></li>
                        <li><a href="#"><?php echo e($post->views_count); ?> Views</a></li>
                      </ul>

                      <?php if($post->excerpt): ?>
                      <div class="post-excerpt">
                        <p><strong><?php echo e($post->excerpt); ?></strong></p>
                      </div>
                      <?php endif; ?>

                      <div class="post-content">
                        <?php echo nl2br(e($post->content)); ?>

                      </div>

                      <div class="post-options">
                        <div class="row">
                          <div class="col-6">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              <?php $__empty_1 = true; $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <li><a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"><?php echo e($tag->name); ?></a><?php if(!$loop->last): ?>,<?php endif; ?></li>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <li>No tags</li>
                              <?php endif; ?>
                            </ul>
                          </div>
                          <div class="col-6">
                            <ul class="post-share">
                              <li><i class="fa fa-share-alt"></i></li>
                              <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->fullUrl())); ?>" target="_blank">Facebook</a>,</li>
                              <li><a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->fullUrl())); ?>&text=<?php echo e(urlencode($post->title)); ?>" target="_blank">Twitter</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Related Posts -->
                <?php if($related_posts->count() > 0): ?>
                <div class="col-lg-12">
                  <div class="sidebar-item">
                    <div class="sidebar-heading">
                      <h2>Related Posts</h2>
                    </div>
                    <div class="content">
                      <div class="row">
                        <?php $__currentLoopData = $related_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related_post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6">
                          <div class="blog-post" style="margin-bottom: 20px;">
                            <div class="blog-thumb">
                              <?php if($related_post->featured_image): ?>
                                <img src="<?php echo e(asset('storage/' . $related_post->featured_image)); ?>" alt="<?php echo e($related_post->title); ?>" style="height: 150px; object-fit: cover;">
                              <?php else: ?>
                                <img src="assets/images/blog-thumb-0<?php echo e(($loop->index % 6) + 1); ?>.jpg" alt="<?php echo e($related_post->title); ?>" style="height: 150px; object-fit: cover;">
                              <?php endif; ?>
                            </div>
                            <div class="down-content">
                              <span><?php echo e($related_post->category->name); ?></span>
                              <a href="<?php echo e(route('post-detail', $related_post->slug)); ?>"><h5><?php echo e(Str::limit($related_post->title, 40)); ?></h5></a>
                              <ul class="post-info">
                                <li><a href="#"><?php echo e($related_post->user->name); ?></a></li>
                                <li><a href="#"><?php echo e($related_post->published_at ? $related_post->published_at->format('M d, Y') : $related_post->created_at->format('M d, Y')); ?></a></li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </div>
                    </div>
                  </div>
                </div>
                <?php endif; ?>

                <!-- Comments Section -->
                <div class="col-lg-12">
                  <div class="sidebar-item comments">
                    <div class="sidebar-heading">
                      <h2>
                        <i class="bi bi-chat-dots me-2"></i>
                        <?php echo e($post->approvedComments->where('parent_id', null)->count()); ?> Comments
                      </h2>
                    </div>
                    <div class="content">
                      <?php if($post->approvedComments->where('parent_id', null)->count() > 0): ?>
                        <div class="comments-list">
                          <?php $__currentLoopData = $post->approvedComments->where('parent_id', null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="comment-item" id="comment-<?php echo e($comment->id); ?>">
                              <!-- Main Comment -->
                              <div class="comment-wrapper">
                                <div class="author-thumb">
                                  <img src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($comment->name)); ?>&background=007bff&color=fff&size=60"
                                       alt="<?php echo e($comment->name); ?>" class="comment-avatar">
                                </div>
                                <div class="comment-content">
                                  <div class="comment-header">
                                    <h5 class="author-name"><?php echo e($comment->name); ?></h5>
                                    <span class="comment-date">
                                      <i class="bi bi-calendar3"></i>
                                      <?php echo e($comment->created_at->format('M d, Y \a\t h:i A')); ?>

                                    </span>
                                  </div>
                                  <div class="comment-text">
                                    <p><?php echo e($comment->comment); ?></p>
                                  </div>
                                </div>
                              </div>

                              <!-- Replies Section -->
                              <?php if($comment->approvedReplies->count() > 0): ?>
                                <div class="replies-section">
                                  <div class="replies-header">
                                    <i class="bi bi-arrow-return-right"></i>
                                    <span class="replies-count"><?php echo e($comment->approvedReplies->count()); ?>

                                      <?php echo e($comment->approvedReplies->count() == 1 ? 'Reply' : 'Replies'); ?>

                                    </span>
                                  </div>

                                  <?php $__currentLoopData = $comment->approvedReplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="reply-item">
                                      <div class="reply-wrapper">
                                        <div class="reply-thumb">
                                          <img src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($reply->name)); ?>&background=28a745&color=fff&size=45"
                                               alt="<?php echo e($reply->name); ?>" class="reply-avatar">
                                        </div>
                                        <div class="reply-content">
                                          <div class="reply-header">
                                            <h6 class="reply-author">
                                              <?php echo e($reply->name); ?>

                                              <?php if($reply->email === '<EMAIL>'): ?>
                                                <span class="admin-badge">
                                                  <i class="bi bi-shield-check"></i> Admin
                                                </span>
                                              <?php endif; ?>
                                            </h6>
                                            <span class="reply-date">
                                              <i class="bi bi-clock"></i>
                                              <?php echo e($reply->created_at->format('M d, Y \a\t h:i A')); ?>

                                            </span>
                                          </div>
                                          <div class="reply-text">
                                            <p><?php echo e($reply->comment); ?></p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                              <?php endif; ?>
                            </div>
                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                      <?php else: ?>
                        <div class="no-comments">
                          <div class="text-center py-5">
                            <i class="bi bi-chat-dots display-4 text-muted mb-3"></i>
                            <h4 class="text-muted">No comments yet</h4>
                            <p class="text-muted">Be the first to share your thoughts!</p>
                          </div>
                        </div>
                      <?php endif; ?>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item submit-comment">
                    <div class="sidebar-heading">
                      <h2>Your comment</h2>
                    </div>
                    <div class="content">
                      <?php if(session('success')): ?>
                        <div class="alert alert-success">
                          <?php echo e(session('success')); ?>

                        </div>
                      <?php endif; ?>

                      <form id="comment" action="<?php echo e(route('post.comment', $post->slug)); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                          <div class="col-md-6 col-sm-12">
                            <fieldset>
                              <input name="name" type="text" id="name" class="name-field" placeholder="Your name" value="<?php echo e(old('name')); ?>" required>
                              <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <small class="text-danger"><?php echo e($message); ?></small>
                              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </fieldset>
                          </div>
                          <div class="col-md-6 col-sm-12">
                            <fieldset>
                              <input name="email" type="email" id="email" placeholder="Your email" value="<?php echo e(old('email')); ?>" required>
                              <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <small class="text-danger"><?php echo e($message); ?></small>
                              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </fieldset>
                          </div>
                          <div class="col-lg-12">
                            <fieldset>
                              <textarea name="comment" rows="6" id="comment-text" placeholder="Type your comment" required><?php echo e(old('comment')); ?></textarea>
                              <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <small class="text-danger"><?php echo e($message); ?></small>
                              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </fieldset>
                          </div>
                          <div class="col-lg-12">
                            <fieldset>
                              <button type="submit" id="form-submit" class="main-button">Submit Comment</button>
                            </fieldset>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="<?php echo e(route('blog')); ?>" style="display: none;">
                      <input type="text" name="search" class="searchText" placeholder="Search posts..." autocomplete="on">
                    </form>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $recent_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recent_post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('post-detail', $recent_post->slug)); ?>">
                          <h5><?php echo e(Str::limit($recent_post->title, 50)); ?></h5>
                          <span><?php echo e($recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y')); ?></span>
                        </a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span><?php echo e(date('M d, Y')); ?></span>
                        </a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('blog', ['category' => $category->slug])); ?>">- <?php echo e($category->name); ?> (<?php echo e($category->posts_count); ?>)</a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">- No categories available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"><?php echo e($tag->name); ?></a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">No tags available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

<?php $__env->stopSection(); ?>

<style>
/* Enhanced Comments Styling */
.comments-list {
  margin-top: 20px;
}

.comment-item {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 25px;
}

.comment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.comment-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.author-thumb {
  flex-shrink: 0;
}

.comment-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.comment-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  position: relative;
  border-left: 4px solid #007bff;
}

.comment-content::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 15px solid #f8f9fa;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 10px;
}

.author-name {
  color: #007bff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.comment-date {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.comment-text p {
  margin: 0;
  color: #495057;
  line-height: 1.6;
  font-size: 15px;
}

/* Replies Section Styling */
.replies-section {
  margin-top: 25px;
  margin-left: 75px; /* Indent replies to the right */
  border-left: 3px solid #28a745;
  padding-left: 20px;
  background: linear-gradient(to right, rgba(40, 167, 69, 0.05), transparent);
  border-radius: 0 10px 10px 0;
}

.replies-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: #28a745;
  font-weight: 600;
  font-size: 14px;
}

.replies-header i {
  font-size: 16px;
}

.reply-item {
  margin-bottom: 20px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.reply-thumb {
  flex-shrink: 0;
}

.reply-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 2px solid #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.reply-content {
  flex: 1;
  background: #ffffff;
  border-radius: 12px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-left: 3px solid #28a745;
  position: relative;
}

.reply-content::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 15px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid #ffffff;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 8px;
}

.reply-author {
  color: #28a745;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-badge {
  background: linear-gradient(135deg, #ffc107, #ff8c00);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.admin-badge i {
  font-size: 10px;
}

.reply-date {
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.reply-text p {
  margin: 0;
  color: #495057;
  line-height: 1.5;
  font-size: 14px;
}

/* No Comments State */
.no-comments {
  text-align: center;
  padding: 40px 20px;
}

.no-comments i {
  color: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .replies-section {
    margin-left: 30px;
    padding-left: 15px;
  }

  .comment-wrapper,
  .reply-wrapper {
    gap: 10px;
  }

  .comment-avatar {
    width: 50px;
    height: 50px;
  }

  .reply-avatar {
    width: 40px;
    height: 40px;
  }

  .comment-content,
  .reply-content {
    padding: 15px;
  }

  .comment-header,
  .reply-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .replies-section {
    margin-left: 20px;
    padding-left: 10px;
  }

  .comment-content::before,
  .reply-content::before {
    display: none;
  }
}

/* Animation for smooth loading */
.comment-item,
.reply-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.comment-content:hover {
  box-shadow: 0 4px 15px rgba(0,123,255,0.1);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.reply-content:hover {
  box-shadow: 0 3px 12px rgba(40, 167, 69, 0.1);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}
</style>
<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/website/post-detail.blade.php ENDPATH**/ ?>