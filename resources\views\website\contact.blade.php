@extends('website.layouts.master')

@section('title', 'Contact Us')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-primary">Get In Touch</h1>
                <p class="lead text-muted">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <form id="contactForm" method="POST" action="{{ route('contact.send') }}">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="name" class="form-label fw-bold">
                                    <i class="bi bi-person-fill text-primary me-2"></i>Full Name
                                </label>
                                <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="email" class="form-label fw-bold">
                                    <i class="bi bi-envelope-fill text-primary me-2"></i>Email Address
                                </label>
                                <input type="email" class="form-control form-control-lg @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="phone" class="form-label fw-bold">
                                    <i class="bi bi-telephone-fill text-primary me-2"></i>Phone Number
                                    <small class="text-muted">(Optional)</small>
                                </label>
                                <input type="tel" class="form-control form-control-lg @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="subject" class="form-label fw-bold">
                                    <i class="bi bi-chat-square-text-fill text-primary me-2"></i>Subject
                                </label>
                                <input type="text" class="form-control form-control-lg @error('subject') is-invalid @enderror" 
                                       id="subject" name="subject" value="{{ old('subject') }}" required>
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="message" class="form-label fw-bold">
                                <i class="bi bi-chat-dots-fill text-primary me-2"></i>Message
                            </label>
                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                      id="message" name="message" rows="6" required 
                                      placeholder="Tell us about your inquiry...">{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5 py-3" id="submitBtn">
                                <i class="bi bi-send-fill me-2"></i>
                                <span class="btn-text">Send Message</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="row mt-5">
                <div class="col-md-4 text-center mb-4">
                    <div class="contact-info-card h-100">
                        <div class="contact-icon mb-3">
                            <i class="bi bi-envelope-fill text-primary fs-1"></i>
                        </div>
                        <h5 class="fw-bold">Email Us</h5>
                        <p class="text-muted"><EMAIL></p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="contact-info-card h-100">
                        <div class="contact-icon mb-3">
                            <i class="bi bi-clock-fill text-primary fs-1"></i>
                        </div>
                        <h5 class="fw-bold">Response Time</h5>
                        <p class="text-muted">Within 24 hours</p>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="contact-info-card h-100">
                        <div class="contact-icon mb-3">
                            <i class="bi bi-chat-heart-fill text-primary fs-1"></i>
                        </div>
                        <h5 class="fw-bold">Support</h5>
                        <p class="text-muted">24/7 Available</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-info-card {
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.contact-icon {
    transition: all 0.3s ease;
}

.contact-info-card:hover .contact-icon {
    transform: scale(1.1);
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

.card {
    border-radius: 20px;
    overflow: hidden;
}

.alert {
    border-radius: 15px;
    border: none;
}

#contactForm {
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    z-index: 1000;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    
    form.addEventListener('submit', function(e) {
        // Add loading state
        submitBtn.disabled = true;
        btnText.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Sending...';
        
        // Create loading overlay
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div class="spinner-border text-primary"></div>';
        form.appendChild(overlay);
    });
    
    // Auto-resize textarea
    const textarea = document.getElementById('message');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Form validation enhancement
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>
@endsection
