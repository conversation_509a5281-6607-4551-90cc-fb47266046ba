@extends('theme.layout.master')

@section('title', 'Posts Management')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-file-text"></i> Posts Management</h1>
    <p>Manage your blog posts</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Posts</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="tile-title">All Posts</h3>
          <a href="{{ route('posts.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add New Post
          </a>
        </div>

        <!-- Search and Filter Section -->
        <form method="GET" action="{{ route('posts.index') }}" class="mb-4">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group dashboard-search-container">
                <input type="text" id="posts-search" class="form-control dashboard-search" placeholder="Search posts..." autocomplete="off">
                <input type="text" name="search" class="form-control" placeholder="Search posts..."
                       value="{{ request('search') }}" style="display: none;">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select name="category" class="form-control">
                  <option value="">All Categories</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                      {{ $category->name }}
                    </option>
                  @endforeach
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select name="status" class="form-control">
                  <option value="">All Status</option>
                  <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Published</option>
                  <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Draft</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> Search
              </button>
              @if(request()->hasAny(['search', 'category', 'status']))
                <a href="{{ route('posts.index') }}" class="btn btn-secondary ms-1">
                  <i class="bi bi-x-circle"></i> Clear
                </a>
              @endif
            </div>
          </div>
        </form>

        @if(session('success'))
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        @endif

        <div class="table-responsive">
          <table class="table table-hover table-bordered" id="postsTable">
            <thead>
              <tr>
                <th>Title</th>
                <th>Category</th>
                <th>Author</th>
                <th>Status</th>
                <th>Published Date</th>
                <th>Views</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @forelse($posts as $post)
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    @if($post->featured_image)
                      <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}" class="rounded me-2" style="width: 50px; height: 50px; object-fit: cover;">
                    @endif
                    <div>
                      <strong>{{ Str::limit($post->title, 50) }}</strong>
                      @if($post->excerpt)
                        <br><small class="text-muted">{{ Str::limit($post->excerpt, 80) }}</small>
                      @endif
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge bg-secondary">{{ $post->category->name }}</span>
                </td>
                <td>{{ $post->user->name }}</td>
                <td>
                  @if($post->status)
                    <span class="badge bg-success">Published</span>
                  @else
                    <span class="badge bg-warning">Draft</span>
                  @endif
                </td>
                <td>
                  @if($post->published_at)
                    {{ $post->published_at->format('M d, Y') }}
                  @else
                    <span class="text-muted">Not published</span>
                  @endif
                </td>
                <td>
                  <span class="badge bg-info">{{ $post->views_count }}</span>
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('posts.show', $post) }}" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="{{ route('posts.edit', $post) }}" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <form action="{{ route('posts.destroy', $post) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
              @empty
              <tr>
                <td colspan="7" class="text-center py-4">
                  <div class="text-muted">
                    <i class="bi bi-file-text fs-1"></i>
                    <p class="mt-2">No posts found. <a href="{{ route('posts.create') }}">Create your first post</a></p>
                  </div>
                </td>
              </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        @if($posts->hasPages())
          <div class="d-flex justify-content-center mt-3">
            {{ $posts->links() }}
          </div>
        @endif
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
  // Initialize DataTable if needed
  // $('#postsTable').DataTable();
</script>
@endpush
