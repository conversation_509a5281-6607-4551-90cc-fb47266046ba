<?php $__env->startSection('title', 'Edit User'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-person-gear"></i> Edit User</h1>
    <p>Update user information</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('users.index')); ?>">Users</a></li>
    <li class="breadcrumb-item active">Edit User</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">User Information</h3>
        
        <form action="<?php echo e(route('users.update', $user)); ?>" method="POST" class="needs-validation" enctype="multipart/form-data" novalidate>
          <?php echo csrf_field(); ?>
          <?php echo method_field('PUT'); ?>

          <!-- Profile Image Upload -->
          <div class="row">
            <div class="col-md-12">
              <div class="mb-4">
                <label class="form-label">Profile Image</label>
                <div class="d-flex align-items-center">
                  <div class="me-3">
                    <img src="<?php echo e($user->profile_image_url); ?>"
                         alt="<?php echo e($user->name); ?>"
                         class="rounded-circle"
                         style="width: 80px; height: 80px; object-fit: cover;"
                         id="current-image">
                  </div>
                  <div class="flex-grow-1">
                    <input type="file" class="form-control <?php $__errorArgs = ['profile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           id="profile_image" name="profile_image" accept="image/*">
                    <?php $__errorArgs = ['profile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                      <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <div class="form-text">
                      Upload JPG, PNG, GIF images. Maximum size: 2MB
                    </div>
                    <?php if($user->profile_image): ?>
                    <div class="mt-2">
                      <form action="<?php echo e(route('users.remove-image', $user)); ?>" method="POST" class="d-inline"
                            onsubmit="return confirm('Are you sure you want to remove the profile image?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-sm btn-outline-danger">
                          <i class="bi bi-trash"></i> Remove Current Image
                        </button>
                      </form>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="name" name="name" value="<?php echo e(old('name', $user->name)); ?>" required>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="mb-3">
                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>" required>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="password" class="form-label">New Password</label>
                <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="password" name="password" placeholder="Leave blank to keep current password">
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <div class="form-text">Minimum 8 characters required</div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="mb-3">
                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" 
                       placeholder="Confirm new password">
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <div class="form-check">
                  <input type="hidden" name="is_admin" value="0">
                  <input type="checkbox" class="form-check-input <?php $__errorArgs = ['is_admin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                         id="is_admin" name="is_admin" value="1" 
                         <?php echo e(old('is_admin', $user->is_admin) ? 'checked' : ''); ?>>
                  <label class="form-check-label" for="is_admin">
                    Administrator Privileges
                  </label>
                  <?php $__errorArgs = ['is_admin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  <div class="form-text">Administrators have full access to the system</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tile-footer">
            <div class="d-flex justify-content-between">
              <a href="<?php echo e(route('users.index')); ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Users
              </a>
              <div>
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle"></i> Update User
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <div class="col-md-4">
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">User Details</h3>
        
        <div class="text-center mb-3">
          <img src="<?php echo e($user->profile_image_url); ?>"
               alt="<?php echo e($user->name); ?>" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
        </div>
        
        <table class="table table-borderless">
          <tr>
            <td><strong>User ID:</strong></td>
            <td><?php echo e($user->id); ?></td>
          </tr>
          <tr>
            <td><strong>Status:</strong></td>
            <td>
              <?php if($user->is_admin): ?>
                <span class="badge bg-danger">Administrator</span>
              <?php else: ?>
                <span class="badge bg-secondary">User</span>
              <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td><strong>Joined:</strong></td>
            <td><?php echo e($user->created_at->format('M d, Y')); ?></td>
          </tr>
          <tr>
            <td><strong>Last Updated:</strong></td>
            <td><?php echo e($user->updated_at->format('M d, Y')); ?></td>
          </tr>
          <?php if($user->posts_count ?? 0 > 0): ?>
          <tr>
            <td><strong>Posts:</strong></td>
            <td>
              <span class="badge bg-info"><?php echo e($user->posts_count ?? 0); ?></span>
            </td>
          </tr>
          <?php endif; ?>
        </table>
        
        <div class="mt-3">
          <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-outline-info btn-sm w-100">
            <i class="bi bi-eye"></i> View Full Profile
          </a>
        </div>
      </div>
    </div>
    
    <!-- Warning Card -->
    <div class="tile mt-3">
      <div class="tile-body">
        <h5 class="text-warning"><i class="bi bi-exclamation-triangle"></i> Important Notes</h5>
        <ul class="small text-muted">
          <li>Leave password fields blank to keep the current password</li>
          <li>Email addresses must be unique in the system</li>
          <li>Administrator privileges grant full system access</li>
          <li>Changes take effect immediately after saving</li>
        </ul>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        // Check password confirmation
        const password = document.getElementById('password').value;
        const passwordConfirm = document.getElementById('password_confirmation').value;
        
        if (password && password !== passwordConfirm) {
            event.preventDefault();
            document.getElementById('password_confirmation').setCustomValidity('Passwords do not match');
        } else {
            document.getElementById('password_confirmation').setCustomValidity('');
        }
        
        form.classList.add('was-validated');
    });
    
    // Real-time password confirmation check
    document.getElementById('password_confirmation').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const passwordConfirm = this.value;

        if (password && passwordConfirm && password !== passwordConfirm) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    document.getElementById('profile_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('current-image').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/users/edit.blade.php ENDPATH**/ ?>