@extends('theme.layout.master')

@section('title', 'View Post')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-eye"></i> View Post</h1>
    <p>{{ $post->title }}</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('posts.index') }}">Posts</a></li>
    <li class="breadcrumb-item active">View</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-body">
        <!-- Post Header -->
        <div class="mb-4">
          <h1 class="display-6">{{ $post->title }}</h1>
          
          <div class="d-flex flex-wrap align-items-center text-muted mb-3">
            <span class="me-3">
              <i class="bi bi-person"></i> {{ $post->user->name }}
            </span>
            <span class="me-3">
              <i class="bi bi-calendar"></i> 
              @if($post->published_at)
                {{ $post->published_at->format('M d, Y') }}
              @else
                Not published
              @endif
            </span>
            <span class="me-3">
              <i class="bi bi-eye"></i> {{ $post->views_count }} views
            </span>
            <span class="me-3">
              <i class="bi bi-clock"></i> {{ $post->reading_time }}
            </span>
          </div>

          <div class="mb-3">
            <span class="badge bg-secondary me-2">{{ $post->category->name }}</span>
            @if($post->status)
              <span class="badge bg-success">Published</span>
            @else
              <span class="badge bg-warning">Draft</span>
            @endif
          </div>

          @if($post->tags->count() > 0)
            <div class="mb-3">
              @foreach($post->tags as $tag)
                <span class="badge me-1" style="background-color: {{ $tag->color }}">
                  {{ $tag->name }}
                </span>
              @endforeach
            </div>
          @endif
        </div>

        <!-- Featured Image -->
        @if($post->featured_image)
          <div class="mb-4">
            <img src="{{ asset('storage/' . $post->featured_image) }}" 
                 alt="{{ $post->title }}" 
                 class="img-fluid rounded">
          </div>
        @endif

        <!-- Excerpt -->
        @if($post->excerpt)
          <div class="mb-4">
            <div class="alert alert-info">
              <strong>Excerpt:</strong> {{ $post->excerpt }}
            </div>
          </div>
        @endif

        <!-- Content -->
        <div class="post-content">
          {!! nl2br(e($post->content)) !!}
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <!-- Actions -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Actions</h3>
      </div>
      <div class="tile-body">
        <div class="d-grid gap-2">
          <a href="{{ route('posts.edit', $post) }}" class="btn btn-warning">
            <i class="bi bi-pencil"></i> Edit Post
          </a>
          <a href="{{ route('posts.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Posts
          </a>
          <form action="{{ route('posts.destroy', $post) }}" method="POST" 
                onsubmit="return confirm('Are you sure you want to delete this post?')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger w-100">
              <i class="bi bi-trash"></i> Delete Post
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Post Information -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Post Information</h3>
      </div>
      <div class="tile-body">
        <table class="table table-sm">
          <tr>
            <td><strong>Status:</strong></td>
            <td>
              @if($post->status)
                <span class="badge bg-success">Published</span>
              @else
                <span class="badge bg-warning">Draft</span>
              @endif
            </td>
          </tr>
          <tr>
            <td><strong>Category:</strong></td>
            <td>{{ $post->category->name }}</td>
          </tr>
          <tr>
            <td><strong>Author:</strong></td>
            <td>{{ $post->user->name }}</td>
          </tr>
          <tr>
            <td><strong>Views:</strong></td>
            <td>{{ $post->views_count }}</td>
          </tr>
          <tr>
            <td><strong>Created:</strong></td>
            <td>{{ $post->created_at->format('M d, Y H:i') }}</td>
          </tr>
          <tr>
            <td><strong>Updated:</strong></td>
            <td>{{ $post->updated_at->format('M d, Y H:i') }}</td>
          </tr>
          @if($post->published_at)
          <tr>
            <td><strong>Published:</strong></td>
            <td>{{ $post->published_at->format('M d, Y H:i') }}</td>
          </tr>
          @endif
        </table>
      </div>
    </div>

    <!-- SEO Information -->
    @if($post->meta_title || $post->meta_description)
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">SEO Information</h3>
      </div>
      <div class="tile-body">
        @if($post->meta_title)
          <div class="mb-3">
            <strong>Meta Title:</strong>
            <p class="text-muted">{{ $post->meta_title }}</p>
          </div>
        @endif
        @if($post->meta_description)
          <div class="mb-3">
            <strong>Meta Description:</strong>
            <p class="text-muted">{{ $post->meta_description }}</p>
          </div>
        @endif
      </div>
    </div>
    @endif

    <!-- Tags -->
    @if($post->tags->count() > 0)
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">Tags</h3>
      </div>
      <div class="tile-body">
        @foreach($post->tags as $tag)
          <span class="badge me-1 mb-1" style="background-color: {{ $tag->color }}">
            {{ $tag->name }}
          </span>
        @endforeach
      </div>
    </div>
    @endif
  </div>
</div>
@endsection

@push('js')
<style>
.post-content {
  line-height: 1.8;
  font-size: 1.1rem;
}

.post-content p {
  margin-bottom: 1.5rem;
}
</style>
@endpush
