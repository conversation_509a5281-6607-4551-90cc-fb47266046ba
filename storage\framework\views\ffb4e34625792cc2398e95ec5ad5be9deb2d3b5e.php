<?php $__env->startSection('title', 'Comments Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-chat-dots"></i> Comments Management</h1>
    <p>Manage all comments from your blog posts</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Comments</li>
  </ul>
</div>

<!-- Success/Error Messages -->
<?php if(session('success')): ?>
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bi bi-check-circle"></i> <?php echo e(session('success')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
<?php endif; ?>

<?php if(session('error')): ?>
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle"></i> <?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
<?php endif; ?>

<?php if($errors->any()): ?>
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle"></i>
    <ul class="mb-0">
      <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><?php echo e($error); ?></li>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="card border-0 shadow-sm h-100 stats-card">
      <div class="card-body text-center">
        <div class="stats-icon mb-3">
          <i class="bi bi-chat-dots text-primary" style="font-size: 2.5rem;"></i>
        </div>
        <h3 class="fw-bold text-dark mb-1"><?php echo e($statistics['total']); ?></h3>
        <p class="text-muted mb-0">Total Comments</p>
      </div>
    </div>
  </div>
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="card border-0 shadow-sm h-100 stats-card">
      <div class="card-body text-center">
        <div class="stats-icon mb-3">
          <i class="bi bi-check-circle text-success" style="font-size: 2.5rem;"></i>
        </div>
        <h3 class="fw-bold text-dark mb-1"><?php echo e($statistics['approved']); ?></h3>
        <p class="text-muted mb-0">Approved</p>
      </div>
    </div>
  </div>
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="card border-0 shadow-sm h-100 stats-card">
      <div class="card-body text-center">
        <div class="stats-icon mb-3">
          <i class="bi bi-clock text-warning" style="font-size: 2.5rem;"></i>
        </div>
        <h3 class="fw-bold text-dark mb-1"><?php echo e($statistics['pending']); ?></h3>
        <p class="text-muted mb-0">Pending</p>
      </div>
    </div>
  </div>
  <div class="col-lg-3 col-md-6 mb-3">
    <div class="card border-0 shadow-sm h-100 stats-card">
      <div class="card-body text-center">
        <div class="stats-icon mb-3">
          <i class="bi bi-calendar-date text-info" style="font-size: 2.5rem;"></i>
        </div>
        <h3 class="fw-bold text-dark mb-1"><?php echo e($statistics['this_month']); ?></h3>
        <p class="text-muted mb-0">This Month</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <!-- Search and Filter Form -->
        <form method="GET" action="<?php echo e(route('comments.index')); ?>" class="mb-4">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="search">Search Comments</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo e(request('search')); ?>" placeholder="Search by name, email, comment, or post title...">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="">All Comments</option>
                  <option value="1" <?php echo e(request('status') == '1' ? 'selected' : ''); ?>>Approved</option>
                  <option value="0" <?php echo e(request('status') == '0' ? 'selected' : ''); ?>>Pending</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>&nbsp;</label>
                <div>
                  <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Search
                  </button>
                  <a href="<?php echo e(route('comments.index')); ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-clockwise"></i> Reset
                  </a>
                </div>
              </div>
            </div>
          </div>
        </form>

        <!-- Comments List -->
        <div class="comments-container">
          <?php $__empty_1 = true; $__currentLoopData = $comments->where('parent_id', null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="comment-card mb-4 <?php echo e($comment->is_approved ? 'approved-comment' : 'pending-comment'); ?>" id="comment-<?php echo e($comment->id); ?>">
              <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                  <!-- Comment Header -->
                  <div class="comment-header mb-4">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="d-flex align-items-center">
                          <div class="avatar-circle me-3">
                            <?php echo e(strtoupper(substr($comment->name, 0, 1))); ?>

                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold text-dark"><?php echo e($comment->name); ?></h6>
                            <div class="comment-meta">
                              <small class="text-muted me-3">
                                <i class="bi bi-envelope"></i> <?php echo e($comment->email); ?>

                              </small>
                              <small class="text-muted me-3">
                                <i class="bi bi-calendar3"></i> <?php echo e($comment->created_at->format('M d, Y h:i A')); ?>

                              </small>
                            </div>
                            <div class="post-info mt-1">
                              <small class="text-primary">
                                <i class="bi bi-file-text"></i>
                                <a href="<?php echo e(route('post-detail', $comment->post->slug)); ?>" target="_blank" class="text-decoration-none">
                                  <?php echo e(Str::limit($comment->post->title, 50)); ?>

                                </a>
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4 text-end">
                        <div class="comment-status">
                          <?php if($comment->is_approved): ?>
                            <span class="badge bg-success fs-6 px-3 py-2">
                              <i class="bi bi-check-circle"></i> Approved
                            </span>
                          <?php else: ?>
                            <span class="badge bg-warning fs-6 px-3 py-2">
                              <i class="bi bi-clock"></i> Pending Approval
                            </span>
                          <?php endif; ?>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Comment Content -->
                  <div class="comment-content mb-4">
                    <div class="comment-text bg-light p-3 rounded">
                      <p class="mb-0 text-dark"><?php echo e($comment->comment); ?></p>
                    </div>
                  </div>

                  <!-- Comment Actions -->
                  <div class="comment-actions">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="action-buttons d-flex gap-2">
                          <?php if(!$comment->is_approved): ?>
                            <form method="POST" action="<?php echo e(route('comments.approve', $comment)); ?>" class="d-inline">
                              <?php echo csrf_field(); ?>
                              <?php echo method_field('PATCH'); ?>
                              <button type="submit" class="btn btn-success btn-sm px-3">
                                <i class="bi bi-check-lg"></i> Approve
                              </button>
                            </form>
                          <?php else: ?>
                            <form method="POST" action="<?php echo e(route('comments.reject', $comment)); ?>" class="d-inline">
                              <?php echo csrf_field(); ?>
                              <?php echo method_field('PATCH'); ?>
                              <button type="submit" class="btn btn-warning btn-sm px-3">
                                <i class="bi bi-x-lg"></i> Reject
                              </button>
                            </form>
                          <?php endif; ?>

                          <button type="button" class="btn btn-primary btn-sm px-3" onclick="toggleReplyForm(<?php echo e($comment->id); ?>)">
                            <i class="bi bi-reply"></i> Reply
                          </button>

                      <form method="POST" action="<?php echo e(route('comments.destroy', $comment)); ?>" class="d-inline"
                            onsubmit="return confirm('Are you sure you want to delete this comment and all its replies?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger btn-sm">
                          <i class="bi bi-trash"></i> Delete
                        </button>
                      </form>
                    </div>

                    <?php if($comment->allReplies->count() > 0): ?>
                      <small class="text-muted">
                        <i class="bi bi-chat-dots"></i> <?php echo e($comment->allReplies->count()); ?>

                        <?php echo e($comment->allReplies->count() == 1 ? 'reply' : 'replies'); ?>

                      </small>
                    <?php endif; ?>
                  </div>

                  <!-- Reply Form -->
                  <div id="reply-form-<?php echo e($comment->id); ?>" class="reply-form mt-3" style="display: none;">
                    <form method="POST" action="<?php echo e(route('comments.reply', $comment)); ?>" onsubmit="console.log('Reply form submitted for comment <?php echo e($comment->id); ?>');">
                      <?php echo csrf_field(); ?>
                      <div class="form-group mb-3">
                        <label for="reply_content_<?php echo e($comment->id); ?>" class="form-label">Your Reply</label>
                        <textarea class="form-control <?php $__errorArgs = ['reply_content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="reply_content_<?php echo e($comment->id); ?>" name="reply_content"
                                  rows="3" placeholder="Write your reply..." required><?php echo e(old('reply_content')); ?></textarea>
                        <?php $__errorArgs = ['reply_content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                          <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                      </div>
                      <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm" id="reply-btn-<?php echo e($comment->id); ?>">
                          <i class="bi bi-send"></i> Post Reply
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="toggleReplyForm(<?php echo e($comment->id); ?>)">
                          Cancel
                        </button>
                      </div>

                      <!-- Success/Error Messages -->
                      <div id="reply-message-<?php echo e($comment->id); ?>" class="mt-2" style="display: none;"></div>
                    </form>
                  </div>

                  <!-- Replies -->
                  <?php if($comment->allReplies->count() > 0): ?>
                    <div class="replies-section mt-4">
                      <h6 class="replies-title">
                        <i class="bi bi-arrow-return-right"></i> Replies (<?php echo e($comment->allReplies->count()); ?>)
                      </h6>
                      <?php $__currentLoopData = $comment->allReplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="reply-card <?php echo e($reply->is_approved ? '' : 'pending-reply'); ?>">
                          <div class="d-flex">
                            <div class="avatar-circle-small me-3">
                              <?php echo e(strtoupper(substr($reply->name, 0, 1))); ?>

                            </div>
                            <div class="flex-grow-1">
                              <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                  <h6 class="mb-0 fw-bold"><?php echo e($reply->name); ?></h6>
                                  <small class="text-muted"><?php echo e($reply->created_at->format('M d, Y h:i A')); ?></small>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                  <?php if($reply->is_approved): ?>
                                    <span class="badge bg-success badge-sm">
                                      <i class="bi bi-check-circle"></i>
                                    </span>
                                  <?php else: ?>
                                    <span class="badge bg-warning badge-sm">
                                      <i class="bi bi-clock"></i>
                                    </span>
                                  <?php endif; ?>

                                  <?php if(!$reply->is_approved): ?>
                                    <form method="POST" action="<?php echo e(route('comments.approve', $reply)); ?>" class="d-inline">
                                      <?php echo csrf_field(); ?>
                                      <?php echo method_field('PATCH'); ?>
                                      <button type="submit" class="btn btn-success btn-xs" title="Approve Reply">
                                        <i class="bi bi-check-lg"></i>
                                      </button>
                                    </form>
                                  <?php endif; ?>

                                  <form method="POST" action="<?php echo e(route('comments.destroy', $reply)); ?>" class="d-inline"
                                        onsubmit="return confirm('Are you sure you want to delete this reply?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-xs" title="Delete Reply">
                                      <i class="bi bi-trash"></i>
                                    </button>
                                  </form>
                                </div>
                              </div>
                              <p class="mb-0"><?php echo e($reply->comment); ?></p>
                            </div>
                          </div>
                        </div>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-center py-5">
              <i class="bi bi-chat-dots display-1 text-muted"></i>
              <h4 class="text-muted mt-3">No comments found</h4>
              <p class="text-muted">Comments will appear here once users start commenting on your posts.</p>
            </div>
          <?php endif; ?>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center">
          <?php echo e($comments->appends(request()->query())->links()); ?>

        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleFullComment(commentId) {
  const fullComment = document.getElementById('full-comment-' + commentId);
  if (fullComment.style.display === 'none') {
    fullComment.style.display = 'block';
  } else {
    fullComment.style.display = 'none';
  }
}
</script>

<style>
/* Statistics Cards */
.stats-card {
  transition: all 0.3s ease;
  border-radius: 15px;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.stats-icon {
  background: rgba(0,123,255,0.1);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Comments Container Styles */
.comments-container {
  max-width: 100%;
}

.comment-card {
  transition: all 0.3s ease;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.comment-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.approved-comment {
  border-left: 5px solid #28a745;
}

.pending-comment {
  border-left: 5px solid #ffc107;
  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.comment-header {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.comment-text {
  border-left: 4px solid #007bff;
  font-size: 1rem;
  line-height: 1.6;
}

/* Avatar Styles */
.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  box-shadow: 0 4px 15px rgba(0,123,255,0.3);
  border: 3px solid #ffffff;
}

.avatar-circle-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 16px;
  background: linear-gradient(135deg, #17a2b8, #138496);
  box-shadow: 0 3px 10px rgba(23,162,184,0.3);
  border: 2px solid #ffffff;
}

/* Action Button Styles */
.action-buttons .btn {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Comment Content Styles */
.comment-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #007bff;
}

.comment-meta {
  font-size: 0.875rem;
}

/* Reply Styles */
.replies-section {
  border-left: 4px solid #e9ecef;
  padding-left: 30px;
  margin-left: 30px;
  margin-top: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 0 15px 15px 0;
  padding-top: 20px;
  padding-bottom: 10px;
}

.replies-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.reply-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  border-left: 4px solid #17a2b8;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.reply-card:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.pending-reply {
  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
  border-left-color: #ffc107;
}

.reply-form {
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  border-radius: 15px;
  padding: 25px;
  border: 2px solid #007bff;
  margin-top: 15px;
}

/* Badge Styles */
.badge {
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-header .col-md-4 {
    text-align: left !important;
    margin-top: 15px;
  }

  .comment-actions .col-md-8 {
    margin-bottom: 15px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .action-buttons .btn {
    margin-bottom: 5px;
  }

  .replies-section {
    margin-left: 15px;
    padding-left: 15px;
  }

  .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 18px;
  }
}

/* Animation for status changes */
.comment-card {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Improved form styling */
.form-control {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Button Styles */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.action-buttons .btn {
  margin-right: 5px;
}

/* Badge Styles */
.badge-sm {
  font-size: 0.75em;
}

/* Statistics Cards */
.widget-small {
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.widget-small:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-card {
    margin-bottom: 20px;
  }

  .replies-section {
    margin-left: 10px;
    padding-left: 15px;
  }

  .action-buttons .btn {
    margin-bottom: 5px;
  }
}

/* Animation for new replies */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-form {
  animation: slideIn 0.3s ease;
}
</style>

<script>
// Toggle reply form visibility
function toggleReplyForm(commentId) {
  const replyForm = document.getElementById('reply-form-' + commentId);
  const isVisible = replyForm.style.display !== 'none';

  // Hide all other reply forms first
  document.querySelectorAll('.reply-form').forEach(form => {
    form.style.display = 'none';
  });

  // Toggle current form
  if (!isVisible) {
    replyForm.style.display = 'block';
    // Focus on textarea
    const textarea = replyForm.querySelector('textarea');
    if (textarea) {
      textarea.focus();
    }
    // Clear any previous messages
    const messageDiv = document.getElementById('reply-message-' + commentId);
    if (messageDiv) {
      messageDiv.style.display = 'none';
      messageDiv.innerHTML = '';
    }
  }
}

// Enhanced form submission handling
document.addEventListener('DOMContentLoaded', function() {
  // Handle all reply forms
  document.querySelectorAll('form[action*="reply"]').forEach(form => {
    form.addEventListener('submit', function(e) {
      const submitBtn = form.querySelector('button[type="submit"]');
      const textarea = form.querySelector('textarea[name="reply_content"]');

      // Basic validation
      if (!textarea.value.trim() || textarea.value.trim().length < 10) {
        e.preventDefault();
        alert('Reply must be at least 10 characters long.');
        return false;
      }

      // Disable submit button to prevent double submission
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Posting...';
      }

      console.log('Reply form submitted:', {
        action: form.action,
        content: textarea.value,
        length: textarea.value.length
      });
    });
  });
});

// Auto-resize textareas
document.addEventListener('DOMContentLoaded', function() {
  const textareas = document.querySelectorAll('textarea');
  textareas.forEach(textarea => {
    textarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
  });

  // Add smooth scrolling to comment actions
  document.querySelectorAll('.comment-actions form').forEach(form => {
    form.addEventListener('submit', function(e) {
      // Add loading state to buttons
      const button = this.querySelector('button[type="submit"]');
      if (button) {
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

        // Re-enable after 3 seconds (in case of errors)
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = originalText;
        }, 3000);
      }
    });
  });
});

// Search functionality enhancement
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search');
  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        // Auto-submit search after 1 second of no typing
        if (this.value.length >= 3 || this.value.length === 0) {
          this.closest('form').submit();
        }
      }, 1000);
    });
  }
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/comments/index.blade.php ENDPATH**/ ?>