<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Comment;
use App\Mail\CommentNotification;
use App\Mail\ContactFormMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class WebsiteController extends Controller
{
    public function index()
    {
        // Get featured/latest posts for homepage
        $featured_posts = Post::with(['category', 'user', 'tags'])
            ->published()
            ->latest('published_at')
            ->limit(6)
            ->get();

        $categories = Category::where('status', true)
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->having('posts_count', '>', 0)
            ->limit(6)
            ->get();

        $tags = Tag::whereHas('posts', function($query) {
                $query->where('status', true);
            })
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->orderBy('posts_count', 'desc')
            ->limit(8)
            ->get();

        return view('website.index', compact('featured_posts', 'categories', 'tags'));
    }

    public function contactUs()
    {
        return view('website.contact-us');
    }

    public function about()
    {
        return view('website.about');
    }

    public function postDetail(Request $request, $slug = null)
    {
        // If no slug provided, try to get from request
        $slug = $slug ?: $request->get('slug');

        if (!$slug) {
            abort(404);
        }



        $post = Post::with(['category', 'user', 'tags', 'approvedComments'])
            ->where('slug', $slug)
            ->where('status', true)
            ->firstOrFail();

        // Increment views count
        $post->increment('views_count');

        // Get related posts
        $related_posts = Post::with(['category', 'user'])
            ->where('category_id', $post->category_id)
            ->where('id', '!=', $post->id)
            ->published()
            ->limit(3)
            ->get();

        // Get sidebar data
        $recent_posts = Post::with(['category'])
            ->published()
            ->where('id', '!=', $post->id)
            ->latest('published_at')
            ->limit(5)
            ->get();

        $categories = Category::where('status', true)
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->having('posts_count', '>', 0)
            ->get();

        $tags = Tag::whereHas('posts', function($query) {
                $query->where('status', true);
            })
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->orderBy('posts_count', 'desc')
            ->limit(10)
            ->get();

        return view('website.post-detail', compact('post', 'related_posts', 'recent_posts', 'categories', 'tags'));
    }

    public function storeComment(Request $request, $slug)
    {
        $post = Post::where('slug', $slug)->published()->firstOrFail();

        $request->validate([
            'name' => 'required|string|min:2|max:255|regex:/^[a-zA-Z\s]+$/',
            'email' => 'required|email|max:255',
            'comment' => 'required|string|min:10|max:1000',
        ], [
            'name.regex' => 'Name can only contain letters and spaces.',
            'name.min' => 'Name must be at least 2 characters long.',
            'comment.min' => 'Comment must be at least 10 characters long.',
        ]);

        $comment = Comment::create([
            'post_id' => $post->id,
            'name' => $request->name,
            'email' => $request->email,
            'comment' => $request->comment,
            'ip_address' => $request->ip(),
            'is_approved' => false, // Comments need approval by default
        ]);

        // Send email notification to admin
        try {
            Mail::to('<EMAIL>')->send(new CommentNotification($comment, 'new'));
        } catch (\Exception $e) {
            // Log error but don't fail the comment submission
            \Log::error('Failed to send new comment notification email: ' . $e->getMessage());
        }

        // Handle AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Your comment has been submitted and is awaiting approval.'
            ]);
        }

        return redirect()->route('post-detail', $slug)
            ->with('success', 'Your comment has been submitted and is awaiting approval.');
    }

    public function searchPosts(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $posts = Post::with(['category', 'user'])
            ->published()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('content', 'like', '%' . $query . '%')
                  ->orWhere('excerpt', 'like', '%' . $query . '%');
            })
            ->limit(10)
            ->get()
            ->map(function($post) {
                return [
                    'id' => $post->id,
                    'title' => $post->title,
                    'excerpt' => Str::limit($post->excerpt ?: $post->content, 100),
                    'category' => $post->category->name,
                    'author' => $post->user->name,
                    'date' => $post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y'),
                    'url' => route('post-detail', $post->slug),
                    'image' => $post->featured_image ? asset('storage/' . $post->featured_image) : null,
                ];
            });

        return response()->json($posts);
    }

    public function blog(Request $request)
    {
        $query = Post::with(['category', 'user', 'tags'])->published();

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filter by tag
        if ($request->has('tag') && $request->tag) {
            $query->whereHas('tags', function($q) use ($request) {
                $q->where('slug', $request->tag);
            });
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%')
                  ->orWhere('excerpt', 'like', '%' . $request->search . '%');
            });
        }

        $posts = $query->latest('published_at')->paginate(6);

        // Get sidebar data
        $recent_posts = Post::with(['category'])
            ->published()
            ->latest('published_at')
            ->limit(5)
            ->get();

        $categories = Category::where('status', true)
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->having('posts_count', '>', 0)
            ->get();

        $tags = Tag::whereHas('posts', function($query) {
                $query->where('status', true);
            })
            ->withCount(['posts' => function($query) {
                $query->where('status', true);
            }])
            ->orderBy('posts_count', 'desc')
            ->limit(10)
            ->get();

        return view('website.blog', compact('posts', 'recent_posts', 'categories', 'tags'));
    }

    public function home()
    {
        return $this->index();
    }

    public function contact()
    {
        return view('website.contact');
    }

    public function sendContactForm(Request $request)
    {
        $request->validate([
            'name' => 'required|string|min:2|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|min:5|max:255',
            'message' => 'required|string|min:10|max:2000',
            'phone' => 'nullable|string|max:20',
        ]);

        $contactData = [
            'name' => $request->name,
            'email' => $request->email,
            'subject' => $request->subject,
            'message' => $request->message,
            'phone' => $request->phone,
            'ip' => $request->ip(),
            'timestamp' => now(),
        ];

        // Send email to admin
        try {
            Mail::to('<EMAIL>')->send(new ContactFormMail($contactData));

            // Handle AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your message! We will get back to you soon.'
                ]);
            }

            return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
        } catch (\Exception $e) {
            \Log::error('Failed to send contact form email: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sorry, there was an error sending your message. Please try again later.'
                ], 500);
            }

            return redirect()->back()->with('error', 'Sorry, there was an error sending your message. Please try again later.');
        }
    }
}

