/**
 * Dashboard Main JavaScript - Additional Features Only
 * Does not override core functionality, only adds enhancements
 */

$(document).ready(function() {
    'use strict';

    // Wait for original main.js to initialize first
    setTimeout(function() {
        initializeEnhancements();
        initializeDataTables();
        initializeFormValidation();
        initializeImageUpload();
    }, 100);

    /**
     * Initialize additional enhancements only
     */
    function initializeEnhancements() {
        console.log('Dashboard enhancements initialized');

        // Add smooth transitions to menu items (non-conflicting)
        $('.app-menu__item').hover(
            function() {
                $(this).addClass('menu-hover');
            },
            function() {
                $(this).removeClass('menu-hover');
            }
        );

        // Add loading states for links
        $('.app-menu a').on('click', function() {
            if ($(this).attr('href') !== '#') {
                $(this).addClass('loading');
            }
        });
    }



    /**
     * Initialize charts
     */
    function initializeCharts() {
        // Monthly Posts Chart
        if (document.getElementById('monthlyPostsChart')) {
            initializeMonthlyPostsChart();
        }

        // Other charts can be initialized here
        if (document.getElementById('categoryChart')) {
            initializeCategoryChart();
        }

        if (document.getElementById('userActivityChart')) {
            initializeUserActivityChart();
        }
    }

    /**
     * Initialize monthly posts chart
     */
    function initializeMonthlyPostsChart() {
        const monthlyData = window.monthlyData || [0,0,0,0,0,0,0,0,0,0,0,0];
        
        const monthlyPostsData = {
            xAxis: { 
                type: 'category', 
                data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] 
            },
            yAxis: { 
                type: 'value',
                axisLabel: { formatter: '{value}' }
            },
            series: [{ 
                data: monthlyData, 
                type: 'bar',
                itemStyle: {
                    color: '#007bff'
                },
                emphasis: {
                    itemStyle: {
                        color: '#0056b3'
                    }
                }
            }],
            tooltip: { 
                trigger: 'axis', 
                formatter: "<b>{b0}:</b> {c0} posts" 
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            }
        };

        const monthlyChart = echarts.init(document.getElementById('monthlyPostsChart'), null, { renderer: 'svg' });
        monthlyChart.setOption(monthlyPostsData);
        
        // Make chart responsive
        new ResizeObserver(() => monthlyChart.resize()).observe(document.getElementById('monthlyPostsChart'));
        
        window.addEventListener('resize', function() {
            monthlyChart.resize();
        });
    }

    /**
     * Initialize DataTables
     */
    function initializeDataTables() {
        if ($.fn.DataTable) {
            // Posts table
            if ($('#postsTable').length) {
                $('#postsTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[4, 'desc']], // Order by date
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable ordering on actions column
                    ]
                });
            }

            // Users table
            if ($('#usersTable').length) {
                $('#usersTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[4, 'desc']], // Order by joined date
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable ordering on actions column
                    ]
                });
            }

            // Categories table
            if ($('#categoriesTable').length) {
                $('#categoriesTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[2, 'desc']], // Order by posts count
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable ordering on actions column
                    ]
                });
            }

            // Tags table
            if ($('#tagsTable').length) {
                $('#tagsTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[4, 'desc']], // Order by posts count
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable ordering on actions column
                    ]
                });
            }
        }
    }

    /**
     * Initialize form validation
     */
    function initializeFormValidation() {
        // Bootstrap validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });

        // Custom validation for specific forms
        $('#postForm').on('submit', function(e) {
            const title = $('input[name="title"]').val();
            const content = $('textarea[name="content"]').val();

            if (!title || title.length < 3) {
                e.preventDefault();
                showAlert('Title must be at least 3 characters long.', 'danger');
                return;
            }

            if (!content || content.length < 10) {
                e.preventDefault();
                showAlert('Content must be at least 10 characters long.', 'danger');
                return;
            }
        });
    }

    /**
     * Initialize image upload functionality
     */
    function initializeImageUpload() {
        $('input[type="file"][accept*="image"]').on('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file size (2MB max)
                if (file.size > 2 * 1024 * 1024) {
                    showAlert('File size must be less than 2MB.', 'danger');
                    $(this).val('');
                    return;
                }

                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    showAlert('Please select a valid image file (JPEG, PNG, JPG, GIF).', 'danger');
                    $(this).val('');
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = $(this).siblings('.image-preview');
                    if (preview.length) {
                        preview.html(`<img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;">`);
                    }
                }.bind(this);
                reader.readAsDataURL(file);
            }
        });
    }

    /**
     * Initialize tooltips and popovers
     */
    function initializeTooltips() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    /**
     * Initialize modals
     */
    function initializeModals() {
        // Confirmation modals for delete actions
        $('[data-confirm]').on('click', function(e) {
            e.preventDefault();
            const message = $(this).data('confirm') || 'Are you sure you want to delete this item?';
            const href = $(this).attr('href') || $(this).closest('form').attr('action');
            
            if (confirm(message)) {
                if ($(this).closest('form').length) {
                    $(this).closest('form').submit();
                } else {
                    window.location.href = href;
                }
            }
        });
    }

    /**
     * Utility functions
     */
    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Remove existing alerts
        $('.alert').remove();
        
        // Add new alert
        $('.app-content').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Auto-hide alerts
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);

    // Loading states for forms
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true).text('Processing...');
        
        // Re-enable after 10 seconds (fallback)
        setTimeout(() => {
            submitBtn.prop('disabled', false).text(originalText);
        }, 10000);
    });

    console.log('Dashboard JavaScript initialized successfully');
});
