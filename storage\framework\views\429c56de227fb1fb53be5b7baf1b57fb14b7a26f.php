<div class="app-sidebar__overlay" data-toggle="sidebar"></div>
<aside class="app-sidebar">
  <div class="app-sidebar__user">
    <img class="app-sidebar__user-avatar" src="<?php echo e(Auth::user()->profile_image_url ?? 'https://ui-avatars.com/api/?name=' . urlencode(Auth::user()->name ?? 'User') . '&background=007bff&color=fff'); ?>" alt="User Image">
    <div>
      <p class="app-sidebar__user-name"><?php echo e(Auth::user()->name ?? 'Guest'); ?></p>
      <p class="app-sidebar__user-designation"><?php echo e(Auth::user()->is_admin ?? false ? 'Administrator' : 'User'); ?></p>
    </div>
  </div>
  <ul class="app-menu">
    <li><a class="app-menu__item <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>"><i class="app-menu__icon bi bi-speedometer"></i><span class="app-menu__label">Dashboard</span></a></li>

    
    <li class="treeview <?php echo e(request()->is('posts*') || request()->is('categories*') || request()->is('tags*') ? 'is-expanded' : ''); ?>">
      <a class="app-menu__item" href="#" data-toggle="treeview">
        <i class="app-menu__icon bi bi-journal-text"></i>
        <span class="app-menu__label">Blog Management</span>
        <i class="treeview-indicator bi bi-chevron-right"></i>
      </a>
      <ul class="treeview-menu">
        <li><a class="treeview-item <?php echo e(request()->routeIs('posts.*') ? 'active' : ''); ?>" href="<?php echo e(route('posts.index')); ?>"><i class="icon bi bi-file-text"></i> Posts</a></li>
        <li><a class="treeview-item <?php echo e(request()->routeIs('categories.*') ? 'active' : ''); ?>" href="<?php echo e(route('categories.index')); ?>"><i class="icon bi bi-folder"></i> Categories</a></li>
        <li><a class="treeview-item <?php echo e(request()->routeIs('tags.*') ? 'active' : ''); ?>" href="<?php echo e(route('tags.index')); ?>"><i class="icon bi bi-tags"></i> Tags</a></li>
      </ul>
    </li>

    
    <li><a class="app-menu__item <?php echo e(request()->routeIs('users.*') ? 'active' : ''); ?>" href="<?php echo e(route('users.index')); ?>"><i class="app-menu__icon bi bi-people"></i><span class="app-menu__label">Users</span><span class="badge bg-primary ms-2"><?php echo e(\App\Models\User::count()); ?></span></a></li>

    
    <li><a class="app-menu__item <?php echo e(request()->routeIs('comments.*') ? 'active' : ''); ?>" href="<?php echo e(route('comments.index')); ?>">
      <i class="app-menu__icon bi bi-chat-dots"></i>
      <span class="app-menu__label">Comments</span>
      <?php
        $totalComments = \App\Models\Comment::count();
        $pendingComments = \App\Models\Comment::where('is_approved', false)->count();
      ?>
      <?php if($pendingComments > 0): ?>
        <span class="badge bg-warning ms-2"><?php echo e($pendingComments); ?></span>
      <?php endif; ?>
      <?php if($totalComments > 0): ?>
        <span class="badge bg-info ms-1"><?php echo e($totalComments); ?></span>
      <?php endif; ?>
    </a></li>

    
    <li class="treeview <?php echo e(request()->is('profile*') || request()->is('settings*') ? 'is-expanded' : ''); ?>">
      <a class="app-menu__item" href="#" data-toggle="treeview">
        <i class="app-menu__icon bi bi-person-gear"></i>
        <span class="app-menu__label">Account</span>
        <i class="treeview-indicator bi bi-chevron-right"></i>
      </a>
      <ul class="treeview-menu">
        <li><a class="treeview-item <?php echo e(request()->routeIs('profile.*') ? 'active' : ''); ?>" href="<?php echo e(route('profile.edit')); ?>"><i class="icon bi bi-person-circle"></i> My Profile</a></li>
        <li><a class="treeview-item <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?>" href="<?php echo e(route('settings.index')); ?>"><i class="icon bi bi-gear"></i> Settings</a></li>
      </ul>
    </li>
  </ul>
</aside>
<?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/template/sidebar.blade.php ENDPATH**/ ?>