<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Post;
use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class PostSlugUniquenessTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user and category for testing
        $this->user = User::factory()->create();
        $this->category = Category::factory()->create();
    }

    /** @test */
    public function it_generates_unique_slug_when_creating_posts_with_same_title()
    {
        Auth::login($this->user);

        // Create first post with title "Test Post"
        $response1 = $this->post(route('posts.store'), [
            'title' => 'Test Post',
            'content' => 'This is test content for the first post.',
            'excerpt' => 'Test excerpt',
            'category_id' => $this->category->id,
            'status' => true,
        ]);

        $response1->assertRedirect(route('posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Post',
            'slug' => 'test-post'
        ]);

        // Create second post with same title "Test Post"
        $response2 = $this->post(route('posts.store'), [
            'title' => 'Test Post',
            'content' => 'This is test content for the second post.',
            'excerpt' => 'Test excerpt 2',
            'category_id' => $this->category->id,
            'status' => true,
        ]);

        $response2->assertRedirect(route('posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Post',
            'slug' => 'test-post-1'
        ]);

        // Create third post with same title "Test Post"
        $response3 = $this->post(route('posts.store'), [
            'title' => 'Test Post',
            'content' => 'This is test content for the third post.',
            'excerpt' => 'Test excerpt 3',
            'category_id' => $this->category->id,
            'status' => true,
        ]);

        $response3->assertRedirect(route('posts.index'));
        $this->assertDatabaseHas('posts', [
            'title' => 'Test Post',
            'slug' => 'test-post-2'
        ]);
    }

    /** @test */
    public function it_generates_unique_slug_when_updating_post_title()
    {
        Auth::login($this->user);

        // Create first post
        $post1 = Post::create([
            'title' => 'Original Title',
            'slug' => 'original-title',
            'content' => 'Original content',
            'excerpt' => 'Original excerpt',
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => true,
        ]);

        // Create second post
        $post2 = Post::create([
            'title' => 'Another Title',
            'slug' => 'another-title',
            'content' => 'Another content',
            'excerpt' => 'Another excerpt',
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => true,
        ]);

        // Update second post to have same title as first post
        $response = $this->put(route('posts.update', $post2), [
            'title' => 'Original Title',
            'content' => 'Updated content',
            'excerpt' => 'Updated excerpt',
            'category_id' => $this->category->id,
            'status' => true,
        ]);

        $response->assertRedirect(route('posts.index'));
        
        // Check that the updated post has a unique slug
        $post2->refresh();
        $this->assertEquals('original-title-1', $post2->slug);
        
        // Check that the first post still has its original slug
        $post1->refresh();
        $this->assertEquals('original-title', $post1->slug);
    }

    /** @test */
    public function it_does_not_change_slug_when_updating_post_without_title_change()
    {
        Auth::login($this->user);

        $post = Post::create([
            'title' => 'Unchanged Title',
            'slug' => 'unchanged-title',
            'content' => 'Original content',
            'excerpt' => 'Original excerpt',
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => true,
        ]);

        // Update post content but not title
        $response = $this->put(route('posts.update', $post), [
            'title' => 'Unchanged Title',
            'content' => 'Updated content',
            'excerpt' => 'Updated excerpt',
            'category_id' => $this->category->id,
            'status' => true,
        ]);

        $response->assertRedirect(route('posts.index'));
        
        // Check that the slug remains unchanged
        $post->refresh();
        $this->assertEquals('unchanged-title', $post->slug);
    }
}
