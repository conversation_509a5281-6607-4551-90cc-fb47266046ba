@extends('theme.layout.master')

@section('title', 'Tags Management')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-tags"></i> Tags Management</h1>
    <p>Manage your blog tags</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Tags</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="tile-title">All Tags</h3>
          <a href="{{ route('tags.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add New Tag
          </a>
        </div>

        @if(session('success'))
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        @endif

        <div class="table-responsive">
          <table class="table table-hover table-bordered" id="tagsTable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Slug</th>
                <th>Description</th>
                <th>Color</th>
                <th>Posts Count</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @forelse($tags as $tag)
              <tr>
                <td>
                  <span class="badge" style="background-color: {{ $tag->color }}">
                    {{ $tag->name }}
                  </span>
                </td>
                <td>
                  <code>{{ $tag->slug }}</code>
                </td>
                <td>
                  @if($tag->description)
                    {{ Str::limit($tag->description, 50) }}
                  @else
                    <span class="text-muted">No description</span>
                  @endif
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <div class="color-preview me-2" 
                         style="width: 20px; height: 20px; background-color: {{ $tag->color }}; border-radius: 3px; border: 1px solid #ddd;"></div>
                    <code>{{ $tag->color }}</code>
                  </div>
                </td>
                <td>
                  <span class="badge bg-info">{{ $tag->posts_count }}</span>
                </td>
                <td>
                  {{ $tag->created_at->format('M d, Y') }}
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('tags.show', $tag) }}" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="{{ route('tags.edit', $tag) }}" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <form action="{{ route('tags.destroy', $tag) }}" method="POST" class="d-inline" 
                          onsubmit="return confirm('Are you sure you want to delete this tag? This will remove it from all associated posts.')">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
              @empty
              <tr>
                <td colspan="7" class="text-center py-4">
                  <div class="text-muted">
                    <i class="bi bi-tags fs-1"></i>
                    <p class="mt-2">No tags found. <a href="{{ route('tags.create') }}">Create your first tag</a></p>
                  </div>
                </td>
              </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        @if($tags->hasPages())
          <div class="d-flex justify-content-center mt-3">
            {{ $tags->links() }}
          </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Tag Statistics -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="widget-small primary coloured-icon">
      <i class="icon bi bi-tags fs-1"></i>
      <div class="info">
        <h4>Total Tags</h4>
        <p><b>{{ $tags->total() }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small info coloured-icon">
      <i class="icon bi bi-file-text fs-1"></i>
      <div class="info">
        <h4>Tagged Posts</h4>
        <p><b>{{ $tags->sum('posts_count') }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small warning coloured-icon">
      <i class="icon bi bi-tag fs-1"></i>
      <div class="info">
        <h4>Most Used</h4>
        <p><b>{{ $tags->sortByDesc('posts_count')->first()?->name ?? 'None' }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small danger coloured-icon">
      <i class="icon bi bi-tag-fill fs-1"></i>
      <div class="info">
        <h4>Unused Tags</h4>
        <p><b>{{ $tags->where('posts_count', 0)->count() }}</b></p>
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
  // Initialize DataTable if needed
  // $('#tagsTable').DataTable();
</script>
@endpush
