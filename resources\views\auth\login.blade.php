@extends('layouts.app')

@section('content')
<section class="material-half-bg">
  <div class="cover"></div>
</section>
<section class="login-content">
  <div class="logo">
    <h1>Vali</h1>
  </div>
  <div class="login-box">
    <form class="login-form" action="{{ route('login') }}" method="POST">
      @csrf
      <h3 class="login-head"><i class="bi bi-person me-2"></i> SIGN IN</h3>
      <div class="mb-3">
        <label class="form-label">USERNAME</label>
        <input name="email" class="form-control" type="text" placeholder="Email" required autofocus>
      </div>
      <div class="mb-3">
        <label class="form-label">PASSWORD</label>
        <input name="password" class="form-control" type="password" placeholder="Password" required>
      </div>
      <div class="mb-3">
        <div class="utility">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="remember">
            <label class="form-check-label">Stay Signed in</label>
          </div>
          <p class="semibold-text mb-2"><a href="#" data-toggle="flip">Forgot Password?</a></p>
        </div>
      </div>
      <div class="mb-3 btn-container d-grid">
        <button class="btn btn-primary btn-block"><i class="bi bi-box-arrow-in-right me-2 fs-5"></i>SIGN IN</button>
      </div>
    </form>
    {{-- Forget form can also go here --}}
  </div>
</section>

<script>
  $('.login-content [data-toggle="flip"]').click(function () {
    $('.login-box').toggleClass('flipped');
    return false;
  });
</script>
@endsection
