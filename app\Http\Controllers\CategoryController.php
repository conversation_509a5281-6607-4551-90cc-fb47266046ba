<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;


class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       $categories = Category::latest()->get();
    return view('dashboard.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
         return view('dashboard.categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
   public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
    ]);

    $baseSlug = Str::slug($request->name);
    $slug = $baseSlug;
    $count = 1;

    // Check for duplicate slugs
    while (Category::where('slug', $slug)->exists()) {
        $slug = $baseSlug . '-' . $count;
        $count++;
    }

    Category::create([
        'name' => $request->name,
        'slug' => $slug,
        'status' => $request->status ?? 1,
    ]);

    return redirect()->route('categories.index')->with('success', 'Category created successfully.');
}



    
        /**
         * Display the specified resource.
         *
         * @param  \App\Models\Category  $category
         * @return \Illuminate\Http\Response
         */
     
    public function show(Category $category)
    {
        return $category;
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function edit(Category $category)
    {
        return view('dashboard.categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
   public function update(Request $request, Category $category)
{
    $request->validate([
        'name' => 'required|string|max:255',
    ]);

    $baseSlug = Str::slug($request->name);
    $slug = $baseSlug;
    $count = 1;

    while (Category::where('slug', $slug)->where('id', '!=', $category->id)->exists()) {
        $slug = $baseSlug . '-' . $count;
        $count++;
    }

    $category->update([
        'name' => $request->name,
        'slug' => $slug,
        'status' => $request->status ?? $category->status,
    ]);

    return redirect()->route('categories.index')->with('success', 'Category updated successfully.');
}

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function destroy(Category $category)
    {
         $category->delete();

        return response()->json(null, 204);
    }
}
