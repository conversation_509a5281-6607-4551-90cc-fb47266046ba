/**
 * Comment Form Validation and Submission
 * Handles form validation, submission, and user feedback
 */

$(document).ready(function() {
    console.log('Comment validation script loaded');
    initializeCommentForm();
});

function initializeCommentForm() {
    const commentForm = $('#comment');

    console.log('Looking for comment form:', commentForm.length);

    if (commentForm.length === 0) {
        console.log('Comment form not found');
        return;
    }

    console.log('Comment form found, initializing validation');

    // Simple form validation without jQuery validate plugin
    commentForm.on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted');

        // Clear previous errors
        $('.text-danger').remove();
        $('.is-invalid, .is-valid').removeClass('is-invalid is-valid');

        let isValid = true;

        // Validate name
        const name = $('input[name="name"]').val().trim();
        if (!name) {
            showFieldError('input[name="name"]', 'Please enter your name');
            isValid = false;
        } else if (name.length < 2) {
            showFieldError('input[name="name"]', 'Name must be at least 2 characters long');
            isValid = false;
        } else if (!/^[a-zA-Z\s]+$/.test(name)) {
            showFieldError('input[name="name"]', 'Name can only contain letters and spaces');
            isValid = false;
        }

        // Validate email
        const email = $('input[name="email"]').val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email) {
            showFieldError('input[name="email"]', 'Please enter your email address');
            isValid = false;
        } else if (!emailRegex.test(email)) {
            showFieldError('input[name="email"]', 'Please enter a valid email address');
            isValid = false;
        }

        // Validate comment
        const comment = $('textarea[name="comment"]').val().trim();
        if (!comment) {
            showFieldError('textarea[name="comment"]', 'Please enter your comment');
            isValid = false;
        } else if (comment.length < 10) {
            showFieldError('textarea[name="comment"]', 'Comment must be at least 10 characters long');
            isValid = false;
        } else if (comment.length > 1000) {
            showFieldError('textarea[name="comment"]', 'Comment cannot exceed 1000 characters');
            isValid = false;
        }

        if (isValid) {
            console.log('Form is valid, submitting...');
            submitComment(this);
        } else {
            console.log('Form validation failed');
        }
    });

    // Real-time character counter for comment textarea
    const commentTextarea = $('#comment textarea[name="comment"]');
    if (commentTextarea.length) {
        // Add character counter
        commentTextarea.after('<small class="text-muted character-counter">0/1000 characters</small>');
        
        commentTextarea.on('input', function() {
            const length = $(this).val().length;
            const counter = $(this).siblings('.character-counter');
            counter.text(length + '/1000 characters');
            
            if (length > 900) {
                counter.removeClass('text-muted').addClass('text-warning');
            } else if (length > 950) {
                counter.removeClass('text-muted text-warning').addClass('text-danger');
            } else {
                counter.removeClass('text-warning text-danger').addClass('text-muted');
            }
        });
    }
}

function showFieldError(fieldSelector, message) {
    const field = $(fieldSelector);
    field.addClass('is-invalid');
    field.after('<small class="text-danger">' + message + '</small>');
}

function submitComment(form) {
    const submitButton = $(form).find('button[type="submit"]');
    const originalText = submitButton.text();
    
    // Show loading state
    submitButton.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Submitting...');
    
    // Remove any existing alerts
    $('.comment-alert').remove();
    
    // Submit form via AJAX
    $.ajax({
        url: $(form).attr('action'),
        method: 'POST',
        data: $(form).serialize(),
        success: function(response) {
            // Show success message
            showCommentAlert('success', 'Your comment has been submitted successfully and is awaiting approval!');
            
            // Reset form
            form.reset();
            $(form).find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
            $('.character-counter').text('0/1000 characters').removeClass('text-warning text-danger').addClass('text-muted');
            
            // Scroll to success message
            $('html, body').animate({
                scrollTop: $('.comment-alert').offset().top - 100
            }, 500);
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while submitting your comment. Please try again.';
            
            if (xhr.status === 422) {
                // Validation errors
                const errors = xhr.responseJSON.errors;
                let errorList = '<ul class="mb-0">';
                
                Object.keys(errors).forEach(function(key) {
                    errors[key].forEach(function(error) {
                        errorList += '<li>' + error + '</li>';
                    });
                });
                
                errorList += '</ul>';
                errorMessage = errorList;
            } else if (xhr.status === 404) {
                errorMessage = 'Post not found. Please refresh the page and try again.';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred. Please try again later.';
            }
            
            showCommentAlert('danger', errorMessage);
            
            // Scroll to error message
            $('html, body').animate({
                scrollTop: $('.comment-alert').offset().top - 100
            }, 500);
        },
        complete: function() {
            // Reset button state
            submitButton.prop('disabled', false).text(originalText);
        }
    });
}

function showCommentAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show comment-alert" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Insert alert before the form
    $('#comment').before(alertHtml);
    
    // Auto-hide success alerts after 5 seconds
    if (type === 'success') {
        setTimeout(function() {
            $('.comment-alert.alert-success').fadeOut();
        }, 5000);
    }
}

// Custom validation method for better name validation
$.validator.addMethod("validName", function(value, element) {
    return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
}, "Please enter a valid name (letters and spaces only)");

// Add the custom validation to the name field
if (typeof $.validator !== 'undefined') {
    $.validator.addClassRules("name-field", {
        validName: true
    });
}
