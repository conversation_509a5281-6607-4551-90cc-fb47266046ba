/**
 * Dashboard Charts JavaScript
 * Handles all chart functionality for the dashboard
 */

/**
 * Initialize monthly posts chart
 */
function initializeMonthlyPostsChart() {
    const monthlyData = window.monthlyData || [0,0,0,0,0,0,0,0,0,0,0,0];
    console.log('Monthly data:', monthlyData);

    const monthlyPostsData = {
        xAxis: {
            type: 'category',
            data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        },
        yAxis: {
            type: 'value',
            axisLabel: { formatter: '{value}' }
        },
        series: [{
            data: monthlyData,
            type: 'bar',
            itemStyle: {
                color: '#007bff'
            },
            emphasis: {
                itemStyle: {
                    color: '#0056b3'
                }
            }
        }],
        tooltip: {
            trigger: 'axis',
            formatter: "<b>{b0}:</b> {c0} posts"
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        }
    };

    const monthlyChart = echarts.init(document.getElementById('monthlyPostsChart'), null, { renderer: 'svg' });
    monthlyChart.setOption(monthlyPostsData);

    // Make chart responsive
    new ResizeObserver(() => monthlyChart.resize()).observe(document.getElementById('monthlyPostsChart'));

    window.addEventListener('resize', function() {
        monthlyChart.resize();
    });
}

/**
 * Initialize category chart
 */
function initializeCategoryChart() {
    const categoryData = window.categoryData || [];
    
    if (categoryData.length === 0) {
        document.getElementById('categoryChart').innerHTML = '<p class="text-center text-muted">No category data available</p>';
        return;
    }

    const categoryChartData = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 10,
            data: categoryData.map(item => item.name)
        },
        series: [
            {
                name: 'Categories',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: categoryData,
                itemStyle: {
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 2
                }
            }
        ],
        color: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14']
    };

    const categoryChart = echarts.init(document.getElementById('categoryChart'), null, { renderer: 'svg' });
    categoryChart.setOption(categoryChartData);
    
    // Make chart responsive
    new ResizeObserver(() => categoryChart.resize()).observe(document.getElementById('categoryChart'));
    
    window.addEventListener('resize', function() {
        categoryChart.resize();
    });
}

/**
 * Initialize user activity chart
 */
function initializeUserActivityChart() {
    const userActivityData = window.userActivityData || [];
    
    if (userActivityData.length === 0) {
        document.getElementById('userActivityChart').innerHTML = '<p class="text-center text-muted">No user activity data available</p>';
        return;
    }

    const activityChartData = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: ['Posts', 'Views', 'Comments']
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: userActivityData.map(item => item.date)
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                name: 'Posts',
                type: 'line',
                stack: 'Total',
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: userActivityData.map(item => item.posts),
                itemStyle: {
                    color: '#007bff'
                }
            },
            {
                name: 'Views',
                type: 'line',
                stack: 'Total',
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: userActivityData.map(item => item.views),
                itemStyle: {
                    color: '#28a745'
                }
            },
            {
                name: 'Comments',
                type: 'line',
                stack: 'Total',
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: userActivityData.map(item => item.comments),
                itemStyle: {
                    color: '#ffc107'
                }
            }
        ]
    };

    const userActivityChart = echarts.init(document.getElementById('userActivityChart'), null, { renderer: 'svg' });
    userActivityChart.setOption(activityChartData);
    
    // Make chart responsive
    new ResizeObserver(() => userActivityChart.resize()).observe(document.getElementById('userActivityChart'));
    
    window.addEventListener('resize', function() {
        userActivityChart.resize();
    });
}

/**
 * Initialize posts trend chart
 */
function initializePostsTrendChart() {
    const trendData = window.postsTrendData || [];
    
    if (trendData.length === 0) {
        document.getElementById('postsTrendChart').innerHTML = '<p class="text-center text-muted">No trend data available</p>';
        return;
    }

    const trendChartData = {
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + params[0].value + ' posts';
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: trendData.map(item => item.period),
            axisLine: {
                lineStyle: {
                    color: '#999'
                }
            }
        },
        yAxis: {
            type: 'value',
            axisLine: {
                lineStyle: {
                    color: '#999'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#f0f0f0'
                }
            }
        },
        series: [
            {
                name: 'Posts',
                type: 'line',
                data: trendData.map(item => item.count),
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    color: '#007bff',
                    width: 3
                },
                itemStyle: {
                    color: '#007bff',
                    borderColor: '#fff',
                    borderWidth: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(0, 123, 255, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(0, 123, 255, 0.05)'
                        }]
                    }
                }
            }
        ]
    };

    const postsTrendChart = echarts.init(document.getElementById('postsTrendChart'), null, { renderer: 'svg' });
    postsTrendChart.setOption(trendChartData);
    
    // Make chart responsive
    new ResizeObserver(() => postsTrendChart.resize()).observe(document.getElementById('postsTrendChart'));
    
    window.addEventListener('resize', function() {
        postsTrendChart.resize();
    });
}

/**
 * Initialize all dashboard charts
 */
function initializeDashboardCharts() {
    // Initialize charts when DOM is ready
    $(document).ready(function() {
        if (document.getElementById('monthlyPostsChart')) {
            initializeMonthlyPostsChart();
        }
        
        if (document.getElementById('categoryChart')) {
            initializeCategoryChart();
        }
        
        if (document.getElementById('userActivityChart')) {
            initializeUserActivityChart();
        }
        
        if (document.getElementById('postsTrendChart')) {
            initializePostsTrendChart();
        }
    });
}

// Auto-initialize charts
initializeDashboardCharts();
