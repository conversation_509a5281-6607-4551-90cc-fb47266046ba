@extends('website.layouts.master')
@section('content')
   <!-- Page Content -->
    <!-- Banner Starts Here -->
    <div class="heading-page header-text">
      <section class="page-heading">
        <div class="container">
          <div class="row">
            <div class="col-lg-12">
              <div class="text-content">
                <h4>{{ $post->category->name }}</h4>
                <h2>{{ $post->title }}</h2>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a rel="nofollow" href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts grid-system">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                <div class="col-lg-12">
                  <div class="blog-post">
                    @if($post->featured_image)
                    <div class="blog-thumb">
                      <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}">
                    </div>
                    @endif
                    <div class="down-content">
                      <span>{{ $post->category->name }}</span>
                      <h4>{{ $post->title }}</h4>
                      <ul class="post-info">
                        <li><a href="#">{{ $post->user->name }}</a></li>
                        <li><a href="#">{{ $post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y') }}</a></li>
                        <li><a href="#">{{ $post->views_count }} Views</a></li>
                      </ul>

                      @if($post->excerpt)
                      <div class="post-excerpt">
                        <p><strong>{{ $post->excerpt }}</strong></p>
                      </div>
                      @endif

                      <div class="post-content">
                        {!! nl2br(e($post->content)) !!}
                      </div>

                      <div class="post-options">
                        <div class="row">
                          <div class="col-6">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              @forelse($post->tags as $tag)
                                <li><a href="{{ route('blog', ['tag' => $tag->slug]) }}">{{ $tag->name }}</a>@if(!$loop->last),@endif</li>
                              @empty
                                <li>No tags</li>
                              @endforelse
                            </ul>
                          </div>
                          <div class="col-6">
                            <ul class="post-share">
                              <li><i class="fa fa-share-alt"></i></li>
                              <li><a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" target="_blank">Facebook</a>,</li>
                              <li><a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($post->title) }}" target="_blank">Twitter</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Related Posts -->
                @if($related_posts->count() > 0)
                <div class="col-lg-12">
                  <div class="sidebar-item">
                    <div class="sidebar-heading">
                      <h2>Related Posts</h2>
                    </div>
                    <div class="content">
                      <div class="row">
                        @foreach($related_posts as $related_post)
                        <div class="col-lg-4 col-md-6">
                          <div class="blog-post" style="margin-bottom: 20px;">
                            <div class="blog-thumb">
                              @if($related_post->featured_image)
                                <img src="{{ asset('storage/' . $related_post->featured_image) }}" alt="{{ $related_post->title }}" style="height: 150px; object-fit: cover;">
                              @else
                                <img src="assets/images/blog-thumb-0{{ ($loop->index % 6) + 1 }}.jpg" alt="{{ $related_post->title }}" style="height: 150px; object-fit: cover;">
                              @endif
                            </div>
                            <div class="down-content">
                              <span>{{ $related_post->category->name }}</span>
                              <a href="{{ route('post-detail', $related_post->slug) }}"><h5>{{ Str::limit($related_post->title, 40) }}</h5></a>
                              <ul class="post-info">
                                <li><a href="#">{{ $related_post->user->name }}</a></li>
                                <li><a href="#">{{ $related_post->published_at ? $related_post->published_at->format('M d, Y') : $related_post->created_at->format('M d, Y') }}</a></li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        @endforeach
                      </div>
                    </div>
                  </div>
                </div>
                @endif

                <!-- Comments Section -->
                <div class="col-lg-12">
                  <div class="sidebar-item comments">
                    <div class="sidebar-heading">
                      <h2>
                        <i class="bi bi-chat-dots me-2"></i>
                        {{ $post->approvedComments->where('parent_id', null)->count() }} Comments
                      </h2>
                    </div>
                    <div class="content">
                      @if($post->approvedComments->where('parent_id', null)->count() > 0)
                        <div class="comments-list">
                          @foreach($post->approvedComments->where('parent_id', null) as $comment)
                            <div class="comment-item" id="comment-{{ $comment->id }}">
                              <!-- Main Comment -->
                              <div class="comment-wrapper">
                                <div class="author-thumb">
                                  <img src="https://ui-avatars.com/api/?name={{ urlencode($comment->name) }}&background=007bff&color=fff&size=60"
                                       alt="{{ $comment->name }}" class="comment-avatar">
                                </div>
                                <div class="comment-content">
                                  <div class="comment-header">
                                    <h5 class="author-name">{{ $comment->name }}</h5>
                                    <span class="comment-date">
                                      <i class="bi bi-calendar3"></i>
                                      {{ $comment->created_at->format('M d, Y \a\t h:i A') }}
                                    </span>
                                  </div>
                                  <div class="comment-text">
                                    <p>{{ $comment->comment }}</p>
                                  </div>
                                </div>
                              </div>

                              <!-- Replies Section -->
                              @if($comment->approvedReplies->count() > 0)
                                <div class="replies-section">
                                  <div class="replies-header">
                                    <i class="bi bi-arrow-return-right"></i>
                                    <span class="replies-count">{{ $comment->approvedReplies->count() }}
                                      {{ $comment->approvedReplies->count() == 1 ? 'Reply' : 'Replies' }}
                                    </span>
                                  </div>

                                  @foreach($comment->approvedReplies as $reply)
                                    <div class="reply-item">
                                      <div class="reply-wrapper">
                                        <div class="reply-thumb">
                                          <img src="https://ui-avatars.com/api/?name={{ urlencode($reply->name) }}&background=28a745&color=fff&size=45"
                                               alt="{{ $reply->name }}" class="reply-avatar">
                                        </div>
                                        <div class="reply-content">
                                          <div class="reply-header">
                                            <h6 class="reply-author">
                                              {{ $reply->name }}
                                              @if($reply->email === '<EMAIL>')
                                                <span class="admin-badge">
                                                  <i class="bi bi-shield-check"></i> Admin
                                                </span>
                                              @endif
                                            </h6>
                                            <span class="reply-date">
                                              <i class="bi bi-clock"></i>
                                              {{ $reply->created_at->format('M d, Y \a\t h:i A') }}
                                            </span>
                                          </div>
                                          <div class="reply-text">
                                            <p>{{ $reply->comment }}</p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  @endforeach
                                </div>
                              @endif
                            </div>
                          @endforeach
                        </div>
                      @else
                        <div class="no-comments">
                          <div class="text-center py-5">
                            <i class="bi bi-chat-dots display-4 text-muted mb-3"></i>
                            <h4 class="text-muted">No comments yet</h4>
                            <p class="text-muted">Be the first to share your thoughts!</p>
                          </div>
                        </div>
                      @endif
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item submit-comment">
                    <div class="sidebar-heading">
                      <h2>Your comment</h2>
                    </div>
                    <div class="content">
                      @if(session('success'))
                        <div class="alert alert-success">
                          {{ session('success') }}
                        </div>
                      @endif

                      <form id="comment" action="{{ route('post.comment', $post->slug) }}" method="post">
                        @csrf
                        <div class="row">
                          <div class="col-md-6 col-sm-12">
                            <fieldset>
                              <input name="name" type="text" id="name" class="name-field" placeholder="Your name" value="{{ old('name') }}" required>
                              @error('name')
                                <small class="text-danger">{{ $message }}</small>
                              @enderror
                            </fieldset>
                          </div>
                          <div class="col-md-6 col-sm-12">
                            <fieldset>
                              <input name="email" type="email" id="email" placeholder="Your email" value="{{ old('email') }}" required>
                              @error('email')
                                <small class="text-danger">{{ $message }}</small>
                              @enderror
                            </fieldset>
                          </div>
                          <div class="col-lg-12">
                            <fieldset>
                              <textarea name="comment" rows="6" id="comment-text" placeholder="Type your comment" required>{{ old('comment') }}</textarea>
                              @error('comment')
                                <small class="text-danger">{{ $message }}</small>
                              @enderror
                            </fieldset>
                          </div>
                          <div class="col-lg-12">
                            <fieldset>
                              <button type="submit" id="form-submit" class="main-button">Submit Comment</button>
                            </fieldset>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="{{ route('blog') }}" style="display: none;">
                      <input type="text" name="search" class="searchText" placeholder="Search posts..." autocomplete="on">
                    </form>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($recent_posts as $recent_post)
                        <li><a href="{{ route('post-detail', $recent_post->slug) }}">
                          <h5>{{ Str::limit($recent_post->title, 50) }}</h5>
                          <span>{{ $recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y') }}</span>
                        </a></li>
                        @empty
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span>{{ date('M d, Y') }}</span>
                        </a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($categories as $category)
                        <li><a href="{{ route('blog', ['category' => $category->slug]) }}">- {{ $category->name }} ({{ $category->posts_count }})</a></li>
                        @empty
                        <li><a href="#">- No categories available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($tags as $tag)
                        <li><a href="{{ route('blog', ['tag' => $tag->slug]) }}">{{ $tag->name }}</a></li>
                        @empty
                        <li><a href="#">No tags available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

@endsection

<style>
/* Enhanced Comments Styling */
.comments-list {
  margin-top: 20px;
}

.comment-item {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 25px;
}

.comment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.comment-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.author-thumb {
  flex-shrink: 0;
}

.comment-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.comment-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  position: relative;
  border-left: 4px solid #007bff;
}

.comment-content::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 15px solid #f8f9fa;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 10px;
}

.author-name {
  color: #007bff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.comment-date {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.comment-text p {
  margin: 0;
  color: #495057;
  line-height: 1.6;
  font-size: 15px;
}

/* Replies Section Styling */
.replies-section {
  margin-top: 25px;
  margin-left: 75px; /* Indent replies to the right */
  border-left: 3px solid #28a745;
  padding-left: 20px;
  background: linear-gradient(to right, rgba(40, 167, 69, 0.05), transparent);
  border-radius: 0 10px 10px 0;
}

.replies-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: #28a745;
  font-weight: 600;
  font-size: 14px;
}

.replies-header i {
  font-size: 16px;
}

.reply-item {
  margin-bottom: 20px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.reply-thumb {
  flex-shrink: 0;
}

.reply-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 2px solid #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.reply-content {
  flex: 1;
  background: #ffffff;
  border-radius: 12px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-left: 3px solid #28a745;
  position: relative;
}

.reply-content::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 15px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid #ffffff;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 8px;
}

.reply-author {
  color: #28a745;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-badge {
  background: linear-gradient(135deg, #ffc107, #ff8c00);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.admin-badge i {
  font-size: 10px;
}

.reply-date {
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.reply-text p {
  margin: 0;
  color: #495057;
  line-height: 1.5;
  font-size: 14px;
}

/* No Comments State */
.no-comments {
  text-align: center;
  padding: 40px 20px;
}

.no-comments i {
  color: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .replies-section {
    margin-left: 30px;
    padding-left: 15px;
  }

  .comment-wrapper,
  .reply-wrapper {
    gap: 10px;
  }

  .comment-avatar {
    width: 50px;
    height: 50px;
  }

  .reply-avatar {
    width: 40px;
    height: 40px;
  }

  .comment-content,
  .reply-content {
    padding: 15px;
  }

  .comment-header,
  .reply-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .replies-section {
    margin-left: 20px;
    padding-left: 10px;
  }

  .comment-content::before,
  .reply-content::before {
    display: none;
  }
}

/* Animation for smooth loading */
.comment-item,
.reply-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.comment-content:hover {
  box-shadow: 0 4px 15px rgba(0,123,255,0.1);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.reply-content:hover {
  box-shadow: 0 3px 12px rgba(40, 167, 69, 0.1);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}
</style>