 <!-- Header -->
    <header class="">
      <nav class="navbar navbar-expand-lg">
        <div class="container">
          <a class="navbar-brand" href="index.html"><h2>Stand Blog<em>.</em></h2></a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive"
  aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
  <span class="navbar-toggler-icon"></span>
</button>

          <div class="collapse navbar-collapse" id="navbarResponsive">
            <ul class="navbar-nav ml-auto">
              <li class="nav-item {{ request()->routeIs('home') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('home') }}">Home
                  <!-- <span class="sr-only">(current)</span> -->
                </a>
              </li>
              <li class="nav-item {{ request()->routeIs('about') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('about') }}">About Us</a>
              </li>
              <li class="nav-item {{ request()->routeIs('blog') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('blog') }}">Blog Entries</a>
              </li>
              <li class="nav-item {{ request()->routeIs('contact*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('contact') }}">Contact Us</a>
              </li>

              @auth
                <!-- Authenticated User Menu -->
                <li class="nav-item">
                  <a class="nav-link" href="{{ route('dashboard') }}">
                    <i class="bi bi-speedometer2"></i> Dashboard
                  </a>
                </li>
                <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle"></i> {{ Auth::user()->name }}
                  </a>
                  <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" href="{{ route('profile.edit') }}">
                      <i class="bi bi-person"></i> Profile
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                      <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="dropdown-item">
                          <i class="bi bi-box-arrow-right"></i> Logout
                        </button>
                      </form>
                    </li>
                  </ul>
                </li>
              @else
                <!-- Guest Menu -->
                <li class="nav-item">
                  <a class="nav-link" href="{{ route('login') }}">
                    <i class="bi bi-box-arrow-in-right"></i> Login
                  </a>
                </li>
                @if (Route::has('register'))
                  <li class="nav-item">
                    <a class="nav-link" href="{{ route('register') }}">
                      <i class="bi bi-person-plus"></i> Register
                    </a>
                  </li>
                @endif
              @endauth
            </ul>
         
          </div>
        </div>
      </nav>
    </header>