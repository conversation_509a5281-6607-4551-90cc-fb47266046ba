@extends('theme.layout.master')

@section('title', 'User Profile')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-person"></i> User Profile</h1>
    <p>View user details and activity</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
    <li class="breadcrumb-item active">{{ $user->name }}</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-4">
    <div class="tile">
      <div class="tile-body text-center">
        <img src="{{ $user->profile_image_url }}"
             alt="{{ $user->name }}" class="rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
        
        <h3>{{ $user->name }}</h3>
        <p class="text-muted">{{ $user->email }}</p>
        
        @if($user->is_admin)
          <span class="badge bg-danger fs-6">Administrator</span>
        @else
          <span class="badge bg-secondary fs-6">User</span>
        @endif
        
        <div class="mt-3">
          <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">
            <i class="bi bi-pencil"></i> Edit User
          </a>
          <a href="{{ route('users.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
          </a>
        </div>
      </div>
    </div>
    
    <!-- User Statistics -->
    <div class="tile mt-3">
      <div class="tile-body">
        <h4 class="tile-title">Statistics</h4>
        <div class="row text-center">
          <div class="col-6">
            <div class="widget-small primary coloured-icon">
              <i class="icon bi bi-file-text fs-1"></i>
              <div class="info">
                <h4>Posts</h4>
                <p><b>{{ $user->posts->count() }}</b></p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="widget-small success coloured-icon">
              <i class="icon bi bi-eye fs-1"></i>
              <div class="info">
                <h4>Total Views</h4>
                <p><b>{{ $user->posts->sum('views_count') }}</b></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-md-8">
    <!-- User Information -->
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">User Information</h3>
        
        <table class="table table-striped">
          <tr>
            <th width="200">User ID</th>
            <td>{{ $user->id }}</td>
          </tr>
          <tr>
            <th>Full Name</th>
            <td>{{ $user->name }}</td>
          </tr>
          <tr>
            <th>Email Address</th>
            <td>{{ $user->email }}</td>
          </tr>
          <tr>
            <th>Role</th>
            <td>
              @if($user->is_admin)
                <span class="badge bg-danger">Administrator</span>
              @else
                <span class="badge bg-secondary">User</span>
              @endif
            </td>
          </tr>
          <tr>
            <th>Email Verified</th>
            <td>
              @if($user->email_verified_at)
                <span class="badge bg-success">Verified</span>
                <small class="text-muted">({{ $user->email_verified_at->format('M d, Y H:i') }})</small>
              @else
                <span class="badge bg-warning">Not Verified</span>
              @endif
            </td>
          </tr>
          <tr>
            <th>Member Since</th>
            <td>{{ $user->created_at->format('F d, Y H:i A') }}</td>
          </tr>
          <tr>
            <th>Last Updated</th>
            <td>{{ $user->updated_at->format('F d, Y H:i A') }}</td>
          </tr>
        </table>
      </div>
    </div>
    
    <!-- User Posts -->
    @if($user->posts->count() > 0)
    <div class="tile mt-3">
      <div class="tile-body">
        <h3 class="tile-title">Recent Posts ({{ $user->posts->count() }})</h3>
        
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Title</th>
                <th>Category</th>
                <th>Status</th>
                <th>Views</th>
                <th>Published</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @foreach($user->posts->take(10) as $post)
              <tr>
                <td>
                  <strong>{{ Str::limit($post->title, 40) }}</strong>
                  @if($post->featured_image)
                    <i class="bi bi-image text-info" title="Has featured image"></i>
                  @endif
                </td>
                <td>
                  @if($post->category)
                    <span class="badge bg-info">{{ $post->category->name }}</span>
                  @else
                    <span class="text-muted">No category</span>
                  @endif
                </td>
                <td>
                  @if($post->status)
                    <span class="badge bg-success">Published</span>
                  @else
                    <span class="badge bg-warning">Draft</span>
                  @endif
                </td>
                <td>
                  <span class="badge bg-secondary">{{ $post->views_count }}</span>
                </td>
                <td>
                  @if($post->published_at)
                    {{ $post->published_at->format('M d, Y') }}
                  @else
                    {{ $post->created_at->format('M d, Y') }}
                  @endif
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="{{ route('posts.show', $post) }}" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="{{ route('posts.edit', $post) }}" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                  </div>
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        
        @if($user->posts->count() > 10)
        <div class="text-center mt-3">
          <a href="{{ route('posts.index', ['user' => $user->id]) }}" class="btn btn-outline-primary">
            View All Posts ({{ $user->posts->count() }})
          </a>
        </div>
        @endif
      </div>
    </div>
    @else
    <div class="tile mt-3">
      <div class="tile-body text-center">
        <i class="bi bi-file-text fs-1 text-muted"></i>
        <h4 class="text-muted mt-2">No Posts Yet</h4>
        <p class="text-muted">This user hasn't created any posts yet.</p>
      </div>
    </div>
    @endif
  </div>
</div>

<!-- Activity Timeline (if needed in future) -->
<div class="row mt-4">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">Recent Activity</h3>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker bg-primary"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">Account Created</h6>
              <p class="timeline-text">User account was created</p>
              <small class="text-muted">{{ $user->created_at->format('F d, Y H:i A') }}</small>
            </div>
          </div>
          
          @if($user->email_verified_at)
          <div class="timeline-item">
            <div class="timeline-marker bg-success"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">Email Verified</h6>
              <p class="timeline-text">Email address was verified</p>
              <small class="text-muted">{{ $user->email_verified_at->format('F d, Y H:i A') }}</small>
            </div>
          </div>
          @endif
          
          @if($user->posts->count() > 0)
          <div class="timeline-item">
            <div class="timeline-marker bg-info"></div>
            <div class="timeline-content">
              <h6 class="timeline-title">First Post</h6>
              <p class="timeline-text">Created first blog post: "{{ $user->posts->first()->title }}"</p>
              <small class="text-muted">{{ $user->posts->first()->created_at->format('F d, Y H:i A') }}</small>
            </div>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endpush
