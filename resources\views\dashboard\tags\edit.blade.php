@extends('theme.layout.master')

@section('title', 'Edit Tag')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-pencil"></i> Edit Tag</h1>
    <p>Update tag: {{ $tag->name }}</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('tags.index') }}">Tags</a></li>
    <li class="breadcrumb-item active">Edit</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-body">
        <form action="{{ route('tags.update', $tag) }}" method="POST">
          @csrf
          @method('PUT')
          
          <!-- Name -->
          <div class="mb-3">
            <label for="name" class="form-label">Tag Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                   id="name" name="name" value="{{ old('name', $tag->name) }}" required 
                   placeholder="Enter tag name">
            @error('name')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">The name of the tag as it will appear on your blog.</div>
          </div>

          <!-- Description -->
          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control @error('description') is-invalid @enderror" 
                      id="description" name="description" rows="4" 
                      placeholder="Optional description for this tag">{{ old('description', $tag->description) }}</textarea>
            @error('description')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">A brief description of what this tag represents (optional).</div>
          </div>

          <!-- Color -->
          <div class="mb-3">
            <label for="color" class="form-label">Tag Color</label>
            <div class="input-group">
              <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                     id="color" name="color" value="{{ old('color', $tag->color) }}" 
                     title="Choose tag color">
              <input type="text" class="form-control @error('color') is-invalid @enderror" 
                     id="color_text" value="{{ old('color', $tag->color) }}" 
                     placeholder="#007bff" pattern="^#[0-9A-Fa-f]{6}$">
            </div>
            @error('color')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Choose a color for this tag. It will be used in tag badges.</div>
          </div>

          <!-- Preview -->
          <div class="mb-4">
            <label class="form-label">Preview</label>
            <div class="p-3 border rounded bg-light">
              <span id="tag-preview" class="badge" style="background-color: {{ old('color', $tag->color) }}">
                <span id="preview-text">{{ old('name', $tag->name) }}</span>
              </span>
            </div>
          </div>

          <div class="tile-footer">
            <div class="d-flex justify-content-between">
              <a href="{{ route('tags.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tags
              </a>
              <div>
                <a href="{{ route('tags.show', $tag) }}" class="btn btn-info me-2">
                  <i class="bi bi-eye"></i> View Tag
                </a>
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle"></i> Update Tag
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <!-- Tag Statistics -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Tag Statistics</h3>
      </div>
      <div class="tile-body">
        <table class="table table-sm">
          <tr>
            <td><strong>Posts Count:</strong></td>
            <td><span class="badge bg-info">{{ $tag->posts_count }}</span></td>
          </tr>
          <tr>
            <td><strong>Created:</strong></td>
            <td>{{ $tag->created_at->format('M d, Y H:i') }}</td>
          </tr>
          <tr>
            <td><strong>Updated:</strong></td>
            <td>{{ $tag->updated_at->format('M d, Y H:i') }}</td>
          </tr>
          <tr>
            <td><strong>Slug:</strong></td>
            <td><code>{{ $tag->slug }}</code></td>
          </tr>
        </table>
      </div>
    </div>

    <!-- Help Card -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Tag Guidelines</h3>
      </div>
      <div class="tile-body">
        <div class="bs-component">
          <div class="alert alert-info">
            <h6><i class="bi bi-info-circle"></i> Tips for editing tags:</h6>
            <ul class="mb-0">
              <li>Keep tag names short and descriptive</li>
              <li>Use consistent naming conventions</li>
              <li>Avoid creating too many similar tags</li>
              <li>Choose colors that are easy to read</li>
              <li>Consider grouping related content</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Color Presets -->
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">Color Presets</h3>
      </div>
      <div class="tile-body">
        <div class="d-flex flex-wrap gap-2">
          <button type="button" class="btn btn-sm color-preset" data-color="#007bff" style="background-color: #007bff; color: white;">Blue</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#28a745" style="background-color: #28a745; color: white;">Green</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#dc3545" style="background-color: #dc3545; color: white;">Red</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#ffc107" style="background-color: #ffc107; color: black;">Yellow</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#17a2b8" style="background-color: #17a2b8; color: white;">Cyan</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#6f42c1" style="background-color: #6f42c1; color: white;">Purple</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#e83e8c" style="background-color: #e83e8c; color: white;">Pink</button>
          <button type="button" class="btn btn-sm color-preset" data-color="#fd7e14" style="background-color: #fd7e14; color: white;">Orange</button>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
  // Update preview when name changes
  document.getElementById('name').addEventListener('input', function() {
    const previewText = document.getElementById('preview-text');
    previewText.textContent = this.value || 'Tag Name';
  });

  // Update preview when color changes
  document.getElementById('color').addEventListener('input', function() {
    const tagPreview = document.getElementById('tag-preview');
    const colorText = document.getElementById('color_text');
    tagPreview.style.backgroundColor = this.value;
    colorText.value = this.value;
  });

  // Update color picker when text input changes
  document.getElementById('color_text').addEventListener('input', function() {
    const colorPicker = document.getElementById('color');
    const tagPreview = document.getElementById('tag-preview');
    if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
      colorPicker.value = this.value;
      tagPreview.style.backgroundColor = this.value;
    }
  });

  // Color preset buttons
  document.querySelectorAll('.color-preset').forEach(button => {
    button.addEventListener('click', function() {
      const color = this.dataset.color;
      const colorPicker = document.getElementById('color');
      const colorText = document.getElementById('color_text');
      const tagPreview = document.getElementById('tag-preview');
      
      colorPicker.value = color;
      colorText.value = color;
      tagPreview.style.backgroundColor = color;
    });
  });
</script>
@endpush
