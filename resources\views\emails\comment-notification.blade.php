<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comment Notification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .comment-box {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .comment-meta {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .comment-content {
            font-size: 16px;
            line-height: 1.8;
            color: #495057;
        }
        .post-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .post-title {
            font-weight: bold;
            color: #007bff;
            text-decoration: none;
            font-size: 18px;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            text-align: center;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .actions {
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>
                @if($type === 'new')
                    New Comment Notification
                @elseif($type === 'reply')
                    New Reply Notification
                @elseif($type === 'approved')
                    Comment Approved
                @endif
            </p>
        </div>

        @if($type === 'new')
            <h2>New Comment on Your Blog Post</h2>
            <p>Hello Admin,</p>
            <p>A new comment has been posted on your blog and is awaiting your approval.</p>
        @elseif($type === 'reply')
            <h2>New Reply to Your Comment</h2>
            <p>Hello {{ $comment->parent->name ?? 'User' }},</p>
            <p>Someone has replied to your comment on the blog post.</p>
        @elseif($type === 'approved')
            <h2>Your Comment Has Been Approved</h2>
            <p>Hello {{ $comment->name }},</p>
            <p>Great news! Your comment has been approved and is now visible on the blog.</p>
        @endif

        <div class="post-info">
            <strong>Post:</strong> 
            <a href="{{ route('post-detail', $comment->post->slug) }}" class="post-title">
                {{ $comment->post->title }}
            </a>
        </div>

        <div class="comment-box">
            <div class="comment-meta">
                <strong>{{ $comment->name }}</strong> ({{ $comment->email }})
                <br>
                <small>{{ $comment->created_at->format('M d, Y h:i A') }}</small>
            </div>
            <div class="comment-content">
                {{ $comment->comment }}
            </div>
        </div>

        @if($comment->parent && $type === 'reply')
            <h4>Original Comment:</h4>
            <div class="comment-box" style="border-left-color: #6c757d;">
                <div class="comment-meta">
                    <strong>{{ $comment->parent->name }}</strong>
                    <br>
                    <small>{{ $comment->parent->created_at->format('M d, Y h:i A') }}</small>
                </div>
                <div class="comment-content">
                    {{ $comment->parent->comment }}
                </div>
            </div>
        @endif

        <div class="actions">
            @if($type === 'new')
                <a href="{{ route('comments.index') }}" class="btn">
                    View in Dashboard
                </a>
                <a href="{{ route('post-detail', $comment->post->slug) }}" class="btn btn-success">
                    View on Website
                </a>
            @else
                <a href="{{ route('post-detail', $comment->post->slug) }}" class="btn">
                    View Comment
                </a>
            @endif
        </div>

        <div class="footer">
            <p>
                This email was sent from {{ config('app.name') }}<br>
                You are receiving this because you are the administrator of this blog.
            </p>
            <p>
                <small>
                    If you don't want to receive these notifications, you can disable them in your dashboard settings.
                </small>
            </p>
        </div>
    </div>
</body>
</html>
