@extends('theme.layout.master')

@section('title', 'Dashboard')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-speedometer"></i> Dashboard</h1>
    <p>Welcome to your blog management dashboard</p>
  </div>
</div>

<!-- Statistics Cards -->
<div class="row">
  <div class="col-md-6 col-lg-3">
    <div class="widget-small primary coloured-icon">
      <i class="icon bi bi-file-text fs-1"></i>
      <div class="info">
        <h4>Total Posts</h4>
        <p><b>{{ $stats['total_posts'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small info coloured-icon">
      <i class="icon bi bi-check-circle fs-1"></i>
      <div class="info">
        <h4>Published</h4>
        <p><b>{{ $stats['published_posts'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small warning coloured-icon">
      <i class="icon bi bi-clock fs-1"></i>
      <div class="info">
        <h4>Drafts</h4>
        <p><b>{{ $stats['draft_posts'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small danger coloured-icon">
      <i class="icon bi bi-eye fs-1"></i>
      <div class="info">
        <h4>Total Views</h4>
        <p><b>{{ number_format($stats['total_views']) }}</b></p>
      </div>
    </div>
  </div>
</div>

<!-- Secondary Statistics -->
<div class="row">
  <div class="col-md-6 col-lg-3">
    <div class="widget-small success coloured-icon">
      <i class="icon bi bi-folder fs-1"></i>
      <div class="info">
        <h4>Categories</h4>
        <p><b>{{ $stats['total_categories'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small secondary coloured-icon">
      <i class="icon bi bi-tags fs-1"></i>
      <div class="info">
        <h4>Tags</h4>
        <p><b>{{ $stats['total_tags'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small dark coloured-icon">
      <i class="icon bi bi-people fs-1"></i>
      <div class="info">
        <h4>Users</h4>
        <p><b>{{ $stats['total_users'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-lg-3">
    <div class="widget-small light coloured-icon">
      <i class="icon bi bi-folder-check fs-1"></i>
      <div class="info">
        <h4>Active Categories</h4>
        <p><b>{{ $stats['active_categories'] }}</b></p>
      </div>
    </div>
  </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Monthly Posts - {{ date('Y') }}</h3>
      <div class="ratio ratio-16x9"><div id="monthlyPostsChart"></div></div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Recent Posts</h3>
      <div class="tile-body">
        @if($recent_posts->count() > 0)
          <div class="list-group list-group-flush">
            @foreach($recent_posts as $post)
            <div class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">{{ Str::limit($post->title, 40) }}</div>
                <small class="text-muted">
                  {{ $post->category->name }} • {{ $post->user->name }} • {{ $post->created_at->diffForHumans() }}
                </small>
              </div>
              <div>
                @if($post->status)
                  <span class="badge bg-success rounded-pill">Published</span>
                @else
                  <span class="badge bg-warning rounded-pill">Draft</span>
                @endif
                <span class="badge bg-info rounded-pill">{{ $post->views_count }} views</span>
              </div>
            </div>
            @endforeach
          </div>
          <div class="mt-3 text-center">
            <a href="{{ route('posts.index') }}" class="btn btn-primary btn-sm">
              <i class="bi bi-arrow-right"></i> View All Posts
            </a>
          </div>
        @else
          <div class="text-center py-4">
            <i class="bi bi-file-text fs-1 text-muted"></i>
            <p class="text-muted mt-2">No posts yet</p>
            <a href="{{ route('posts.create') }}" class="btn btn-primary">
              <i class="bi bi-plus-circle"></i> Create First Post
            </a>
          </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Popular Posts and Quick Actions -->
<div class="row">
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Popular Posts</h3>
      <div class="tile-body">
        @if($popular_posts->count() > 0)
          <div class="list-group list-group-flush">
            @foreach($popular_posts as $post)
            <div class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">{{ Str::limit($post->title, 40) }}</div>
                <small class="text-muted">
                  {{ $post->category->name }} • {{ $post->published_at ? $post->published_at->format('M d, Y') : 'Not published' }}
                </small>
              </div>
              <span class="badge bg-primary rounded-pill">{{ $post->views_count }} views</span>
            </div>
            @endforeach
          </div>
        @else
          <div class="text-center py-4">
            <i class="bi bi-graph-up fs-1 text-muted"></i>
            <p class="text-muted mt-2">No popular posts yet</p>
          </div>
        @endif
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Quick Actions</h3>
      <div class="tile-body">
        <div class="d-grid gap-2">
          <a href="{{ route('posts.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Create New Post
          </a>
          <a href="{{ route('categories.create') }}" class="btn btn-success">
            <i class="bi bi-folder-plus"></i> Add Category
          </a>
          <a href="{{ route('tags.create') }}" class="btn btn-info">
            <i class="bi bi-tag"></i> Create Tag
          </a>
          <a href="{{ route('posts.index') }}" class="btn btn-secondary">
            <i class="bi bi-list"></i> Manage Posts
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
  // Pass data to global scope for charts
  window.monthlyData = {!! json_encode($monthly_data) !!};
  window.categoryData = {!! json_encode($category_data ?? []) !!};
  window.userActivityData = {!! json_encode($user_activity_data ?? []) !!};
  window.postsTrendData = {!! json_encode($posts_trend_data ?? []) !!};
</script>
<script src="{{ asset('js/dashboard/charts.js') }}"></script>
@endpush
