/**
 * Website Main JavaScript
 * Handles all website functionality including search, filters, and interactions
 */

$(document).ready(function() {
    'use strict';

    // Initialize all components
    initializeSearch();
    initializeFilters();
    initializeAnimations();
    initializeLazyLoading();
    initializeShareButtons();
    initializeComments();

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        const searchForm = $('#search_form');
        const searchInput = $('.searchText');

        // Auto-submit search form on Enter
        searchInput.on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                searchForm.submit();
            }
        });

        // Search suggestions (if needed in future)
        searchInput.on('input', debounce(function() {
            const query = $(this).val();
            if (query.length > 2) {
                // Implement search suggestions here
                console.log('Searching for:', query);
            }
        }, 300));
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        // Filter removal
        $('.remove-filter').on('click', function(e) {
            e.preventDefault();
            const url = $(this).attr('href');
            window.location.href = url;
        });

        // Clear all filters
        $('.clear-all-filters').on('click', function(e) {
            e.preventDefault();
            const url = $(this).attr('href');
            window.location.href = url;
        });

        // Category and tag filters
        $('.sidebar-item a[href*="category"], .sidebar-item a[href*="tag"]').on('click', function(e) {
            // Add loading state
            $(this).addClass('loading');
        });
    }

    /**
     * Initialize animations and hover effects
     */
    function initializeAnimations() {
        // Blog post hover effects
        $('.blog-post').hover(
            function() {
                $(this).addClass('animated');
            },
            function() {
                $(this).removeClass('animated');
            }
        );

        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });

        // Fade in elements on scroll
        $(window).on('scroll', function() {
            $('.fade-in').each(function() {
                const elementTop = $(this).offset().top;
                const elementBottom = elementTop + $(this).outerHeight();
                const viewportTop = $(window).scrollTop();
                const viewportBottom = viewportTop + $(window).height();

                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $(this).addClass('visible');
                }
            });
        });
    }

    /**
     * Initialize lazy loading for images
     */
    function initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Initialize share buttons
     */
    function initializeShareButtons() {
        $('.post-share a').on('click', function(e) {
            const url = $(this).attr('href');
            if (url.includes('facebook.com') || url.includes('twitter.com')) {
                e.preventDefault();
                window.open(url, 'share', 'width=600,height=400,scrollbars=yes,resizable=yes');
            }
        });

        // Copy link functionality
        if ($('.copy-link').length) {
            $('.copy-link').on('click', function(e) {
                e.preventDefault();
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(function() {
                    showNotification('Link copied to clipboard!', 'success');
                });
            });
        }
    }

    /**
     * Initialize comment functionality
     */
    function initializeComments() {
        const commentForm = $('#comment');
        
        if (commentForm.length) {
            commentForm.on('submit', function(e) {
                e.preventDefault();
                
                // Basic validation
                const name = $('input[name="name"]').val();
                const email = $('input[name="email"]').val();
                const message = $('textarea[name="message"]').val();

                if (!name || !email || !message) {
                    showNotification('Please fill in all required fields.', 'error');
                    return;
                }

                if (!isValidEmail(email)) {
                    showNotification('Please enter a valid email address.', 'error');
                    return;
                }

                // Submit form
                const formData = new FormData(this);
                submitComment(formData);
            });
        }
    }

    /**
     * Submit comment via AJAX
     */
    function submitComment(formData) {
        $.ajax({
            url: '/contact-us',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('#comment button[type="submit"]').prop('disabled', true).text('Sending...');
            },
            success: function(response) {
                showNotification('Thank you for your message! We will get back to you soon.', 'success');
                $('#comment')[0].reset();
            },
            error: function(xhr) {
                let message = 'An error occurred. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showNotification(message, 'error');
            },
            complete: function() {
                $('#comment button[type="submit"]').prop('disabled', false).text('Send Message');
            }
        });
    }

    /**
     * Utility functions
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = $(`
            <div class="notification notification-${type}">
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            </div>
        `);

        // Add to page
        $('body').append(notification);

        // Show notification
        setTimeout(() => {
            notification.addClass('show');
        }, 100);

        // Auto hide after 5 seconds
        setTimeout(() => {
            hideNotification(notification);
        }, 5000);

        // Close button
        notification.find('.notification-close').on('click', function() {
            hideNotification(notification);
        });
    }

    function hideNotification(notification) {
        notification.removeClass('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }

    // Reading progress bar
    if ($('.post-content').length) {
        const progressBar = $('<div class="reading-progress"><div class="reading-progress-bar"></div></div>');
        $('body').prepend(progressBar);

        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const docHeight = $(document).height() - $(window).height();
            const scrollPercent = (scrollTop / docHeight) * 100;
            $('.reading-progress-bar').css('width', scrollPercent + '%');
        });
    }

    // Back to top button
    const backToTop = $('<button class="back-to-top" title="Back to Top"><i class="fa fa-arrow-up"></i></button>');
    $('body').append(backToTop);

    $(window).on('scroll', function() {
        if ($(window).scrollTop() > 300) {
            backToTop.addClass('show');
        } else {
            backToTop.removeClass('show');
        }
    });

    backToTop.on('click', function() {
        $('html, body').animate({ scrollTop: 0 }, 800);
    });

    console.log('Website JavaScript initialized successfully');
});
