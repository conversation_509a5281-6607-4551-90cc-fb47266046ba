<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tags', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->text('description')->nullable()->after('slug');
            $table->string('color', 7)->default('#007bff')->after('description');
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tags', function (Blueprint $table) {
            $table->dropColumn(['description', 'color']);
            $table->boolean('status')->default(true)->after('slug');
            $table->dropIndex(['slug']);
        });
    }
};
