<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Submission</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 28px;
        }
        .contact-info {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .info-row {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            min-width: 100px;
            margin-right: 15px;
        }
        .info-value {
            color: #6c757d;
        }
        .message-box {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .message-content {
            font-size: 16px;
            line-height: 1.8;
            color: #495057;
            white-space: pre-wrap;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            text-align: center;
        }
        .btn:hover {
            background: #1e7e34;
        }
        .actions {
            text-align: center;
            margin: 30px 0;
        }
        .timestamp {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>New Contact Form Submission</p>
        </div>

        <div class="timestamp">
            <strong>Received:</strong> {{ now()->format('M d, Y h:i A') }}
        </div>

        <h2>Contact Information</h2>
        <div class="contact-info">
            <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-value">{{ $contactData['name'] }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">
                    <a href="mailto:{{ $contactData['email'] }}">{{ $contactData['email'] }}</a>
                </span>
            </div>
            @if(isset($contactData['phone']) && $contactData['phone'])
            <div class="info-row">
                <span class="info-label">Phone:</span>
                <span class="info-value">{{ $contactData['phone'] }}</span>
            </div>
            @endif
            <div class="info-row">
                <span class="info-label">Subject:</span>
                <span class="info-value">{{ $contactData['subject'] }}</span>
            </div>
        </div>

        <h3>Message</h3>
        <div class="message-box">
            <div class="message-content">{{ $contactData['message'] }}</div>
        </div>

        <div class="actions">
            <a href="mailto:{{ $contactData['email'] }}?subject=Re: {{ $contactData['subject'] }}" class="btn">
                Reply to {{ $contactData['name'] }}
            </a>
        </div>

        <div class="footer">
            <p>
                This email was sent from the contact form on {{ config('app.name') }}<br>
                <strong>Sender IP:</strong> {{ request()->ip() ?? 'Unknown' }}
            </p>
            <p>
                <small>
                    Please respond to this inquiry as soon as possible to maintain good customer relations.
                </small>
            </p>
        </div>
    </div>
</body>
</html>
