/**
 * AJAX Search Styles
 */

.search-results {
    border-radius: 0 0 8px 8px;
    background: #fff;
    border: 1px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-item-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.search-item-image {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.search-item-avatar {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    flex-shrink: 0;
}

.search-item-details {
    flex: 1;
    min-width: 0;
}

.search-item-title {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.search-item-excerpt {
    margin: 0 0 6px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-item-meta {
    font-size: 11px;
    color: #888;
}

.search-item-meta .category {
    color: #007bff;
    font-weight: 500;
}

.search-item-meta .status {
    color: #28a745;
    font-weight: 500;
}

.search-item-meta .role {
    color: #dc3545;
    font-weight: 500;
}

.search-loading,
.search-no-results,
.search-error {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.search-loading {
    color: #007bff;
}

.search-error {
    color: #dc3545;
}

.search-no-results {
    color: #6c757d;
}

/* Website specific styles */
.website-search-container {
    position: relative;
}

.website-search {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.website-search:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Dashboard specific styles */
.dashboard-search-container {
    position: relative;
}

.dashboard-search {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-search:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-item-content {
        gap: 8px;
    }
    
    .search-item-image {
        width: 50px;
        height: 38px;
    }
    
    .search-item-avatar {
        width: 35px;
        height: 35px;
    }
    
    .search-item-title {
        font-size: 13px;
    }
    
    .search-item-excerpt {
        font-size: 11px;
    }
    
    .search-item-meta {
        font-size: 10px;
    }
}

/* Animation for results appearing */
.search-results {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.search-loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Highlight search terms */
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 600;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .search-results {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .search-result-item:hover {
        background-color: #374151;
    }
    
    .search-item-title {
        color: #f7fafc;
    }
    
    .search-item-excerpt {
        color: #a0aec0;
    }
    
    .search-item-meta {
        color: #718096;
    }
}

/* Comment Form Validation Styles */
.comment-alert {
    margin-bottom: 20px;
    border-radius: 5px;
}

.character-counter {
    display: block;
    text-align: right;
    margin-top: 5px;
    font-size: 0.875rem;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.text-danger {
    color: #dc3545 !important;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

/* Comment form specific styles */
#comment fieldset {
    position: relative;
    margin-bottom: 15px;
}

#comment input, #comment textarea {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#comment input:focus, #comment textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button loading state */
.main-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Comment form improvements */
.submit-comment {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
}

.submit-comment .sidebar-heading h2 {
    color: #333;
    margin-bottom: 20px;
}

.submit-comment fieldset {
    border: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.submit-comment input,
.submit-comment textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.submit-comment input:focus,
.submit-comment textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    outline: none;
}

.submit-comment .main-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-comment .main-button:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

/* Comment display improvements */
.comments ul {
    list-style: none;
    padding: 0;
}

.comments li {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.comments .author-thumb img {
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.comments .right-content h4 {
    color: #333;
    margin-bottom: 10px;
}

.comments .right-content h4 span {
    color: #6c757d;
    font-size: 14px;
    font-weight: normal;
    margin-left: 10px;
}

.comments .right-content p {
    color: #555;
    line-height: 1.6;
    margin: 0;
}

/* Website Navigation Enhancements */
.navbar-nav .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-top: 5px;
}

.navbar-nav .dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.navbar-nav .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.navbar-nav .dropdown-item i {
    margin-right: 8px;
    width: 16px;
}

.navbar-nav .nav-link i {
    margin-right: 5px;
}

/* Dashboard button styling */
.navbar-nav .nav-link[href*="dashboard"] {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white !important;
    border-radius: 20px;
    padding: 8px 16px !important;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link[href*="dashboard"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* User dropdown styling */
.navbar-nav .dropdown-toggle::after {
    margin-left: 8px;
}

/* Responsive improvements */
@media (max-width: 991px) {
    .navbar-nav .dropdown-menu {
        border: 1px solid #dee2e6;
        box-shadow: none;
    }

    .navbar-nav .nav-link[href*="dashboard"] {
        border-radius: 5px;
        margin: 5px 0;
    }
}
