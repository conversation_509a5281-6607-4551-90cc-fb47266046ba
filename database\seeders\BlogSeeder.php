<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Tag;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create sample categories (only if they don't exist)
        $categories = [
            ['name' => 'Technology', 'description' => 'Latest tech news and tutorials'],
            ['name' => 'Lifestyle', 'description' => 'Life tips and experiences'],
            ['name' => 'Travel', 'description' => 'Travel guides and experiences'],
            ['name' => 'Food', 'description' => 'Recipes and food reviews'],
            ['name' => 'Health', 'description' => 'Health and wellness tips'],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['slug' => Str::slug($category['name'])],
                [
                    'name' => $category['name'],
                    'description' => $category['description'],
                    'status' => true,
                ]
            );
        }

        // Create sample tags (only if they don't exist)
        $tags = [
            ['name' => 'Laravel', 'color' => '#ff2d20'],
            ['name' => 'PHP', 'color' => '#777bb4'],
            ['name' => 'JavaScript', 'color' => '#f7df1e'],
            ['name' => 'Vue.js', 'color' => '#4fc08d'],
            ['name' => 'Tutorial', 'color' => '#007bff'],
            ['name' => 'Tips', 'color' => '#28a745'],
            ['name' => 'Guide', 'color' => '#17a2b8'],
            ['name' => 'Review', 'color' => '#ffc107'],
        ];

        foreach ($tags as $tag) {
            Tag::firstOrCreate(
                ['slug' => Str::slug($tag['name'])],
                [
                    'name' => $tag['name'],
                    'color' => $tag['color'],
                    'description' => 'Sample tag for ' . $tag['name'],
                ]
            );
        }

        // Get first user (assuming admin user exists)
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'is_admin' => true,
            ]);
        }

        // Get created categories
        $techCategory = Category::where('name', 'Technology')->first();
        $lifestyleCategory = Category::where('name', 'Lifestyle')->first();
        $travelCategory = Category::where('name', 'Travel')->first();
        $healthCategory = Category::where('name', 'Health')->first();

        // Get created tags
        $laravelTag = Tag::where('name', 'Laravel')->first();
        $phpTag = Tag::where('name', 'PHP')->first();
        $jsTag = Tag::where('name', 'JavaScript')->first();
        $vueTag = Tag::where('name', 'Vue.js')->first();
        $tutorialTag = Tag::where('name', 'Tutorial')->first();
        $tipsTag = Tag::where('name', 'Tips')->first();
        $guideTag = Tag::where('name', 'Guide')->first();
        $reviewTag = Tag::where('name', 'Review')->first();

        // Create sample posts
        $posts = [
            [
                'title' => 'Getting Started with Laravel 10',
                'excerpt' => 'Learn the basics of Laravel 10 and build your first web application.',
                'content' => 'Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects.',
                'category_id' => $techCategory->id,
                'tags' => [$laravelTag->id, $phpTag->id, $tutorialTag->id],
            ],
            [
                'title' => 'Top 10 Travel Destinations for 2024',
                'excerpt' => 'Discover the most amazing places to visit this year.',
                'content' => 'Travel is the movement of people between distant geographical locations. Travel can be done by foot, bicycle, automobile, train, boat, bus, airplane, ship or other means, with or without luggage.',
                'category_id' => $travelCategory->id,
                'tags' => [$reviewTag->id],
            ],
            [
                'title' => 'Healthy Eating Habits for Busy People',
                'excerpt' => 'Simple tips to maintain a healthy diet even with a busy schedule.',
                'content' => 'A healthy diet is a diet that maintains or improves overall health. A healthy diet provides the body with essential nutrition: fluid, macronutrients, micronutrients, and adequate food energy.',
                'category_id' => $healthCategory->id,
                'tags' => [$tipsTag->id, $guideTag->id],
            ],
            [
                'title' => 'Building Modern Web Apps with Vue.js',
                'excerpt' => 'A comprehensive guide to Vue.js development.',
                'content' => 'Vue.js is an open-source model–view–viewmodel front end JavaScript framework for building user interfaces and single-page applications. It was created by Evan You, and is maintained by him and the rest of the active core team members.',
                'category_id' => $techCategory->id,
                'tags' => [$jsTag->id, $vueTag->id, $tutorialTag->id],
            ],
            [
                'title' => 'The Art of Minimalist Living',
                'excerpt' => 'How to simplify your life and focus on what matters most.',
                'content' => 'Minimalism is a tool to rid yourself of life\'s excess in favor of focusing on what\'s important—so you can find happiness, fulfillment, and freedom.',
                'category_id' => $lifestyleCategory->id,
                'tags' => [$tipsTag->id, $guideTag->id],
            ],
        ];

        foreach ($posts as $postData) {
            $post = Post::create([
                'title' => $postData['title'],
                'slug' => Str::slug($postData['title']),
                'excerpt' => $postData['excerpt'],
                'content' => $postData['content'],
                'status' => true,
                'published_at' => now()->subDays(rand(1, 30)),
                'category_id' => $postData['category_id'],
                'user_id' => $user->id,
                'views_count' => rand(10, 500),
            ]);

            // Attach tags
            $post->tags()->attach($postData['tags']);
        }
    }
}
