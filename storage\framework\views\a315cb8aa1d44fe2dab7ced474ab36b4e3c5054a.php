<?php $__env->startSection('title', 'Users Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-people"></i> Users Management</h1>
    <p>Manage system users</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Users</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="tile-title">All Users</h3>
          <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add New User
          </a>
        </div>

        <!-- Search and Filter Section -->
        <form method="GET" action="<?php echo e(route('users.index')); ?>" class="mb-4">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group dashboard-search-container">
                <input type="text" id="users-search" class="form-control dashboard-search" placeholder="Search users by name or email..." autocomplete="off">
                <input type="text" name="search" class="form-control" placeholder="Search users by name or email..."
                       value="<?php echo e(request('search')); ?>" style="display: none;">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <select name="role" class="form-control">
                  <option value="">All Roles</option>
                  <option value="1" <?php echo e(request('role') === '1' ? 'selected' : ''); ?>>Administrator</option>
                  <option value="0" <?php echo e(request('role') === '0' ? 'selected' : ''); ?>>User</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> Search
              </button>
              <?php if(request()->hasAny(['search', 'role'])): ?>
                <a href="<?php echo e(route('users.index')); ?>" class="btn btn-secondary ms-1">
                  <i class="bi bi-x-circle"></i> Clear
                </a>
              <?php endif; ?>
            </div>
          </div>
        </form>

        <?php if(session('success')): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        <?php endif; ?>

        <div class="table-responsive">
          <table class="table table-hover table-bordered" id="usersTable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Posts Count</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    <img src="<?php echo e($user->profile_image_url); ?>"
                         alt="<?php echo e($user->name); ?>" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                    <div>
                      <strong><?php echo e($user->name); ?></strong>
                    </div>
                  </div>
                </td>
                <td><?php echo e($user->email); ?></td>
                <td>
                  <?php if($user->is_admin): ?>
                    <span class="badge bg-danger">Administrator</span>
                  <?php else: ?>
                    <span class="badge bg-secondary">User</span>
                  <?php endif; ?>
                </td>
                <td>
                  <span class="badge bg-info"><?php echo e($user->posts_count); ?></span>
                </td>
                <td>
                  <?php echo e($user->created_at->format('M d, Y')); ?>

                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="<?php echo e(route('users.edit', $user)); ?>" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <?php if(!$user->is_admin || \App\Models\User::where('is_admin', true)->count() > 1): ?>
                    <form action="<?php echo e(route('users.destroy', $user)); ?>" method="POST" class="d-inline" 
                          onsubmit="return confirm('Are you sure you want to delete this user?')">
                      <?php echo csrf_field(); ?>
                      <?php echo method_field('DELETE'); ?>
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                    <?php endif; ?>
                  </div>
                </td>
              </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
              <tr>
                <td colspan="6" class="text-center py-4">
                  <div class="text-muted">
                    <i class="bi bi-people fs-1"></i>
                    <p class="mt-2">No users found.</p>
                  </div>
                </td>
              </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>

        <?php if($users->hasPages()): ?>
          <div class="d-flex justify-content-center mt-3">
            <?php echo e($users->links()); ?>

          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<!-- User Statistics -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="widget-small primary coloured-icon">
      <i class="icon bi bi-people fs-1"></i>
      <div class="info">
        <h4>Total Users</h4>
        <p><b><?php echo e($users->total()); ?></b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small danger coloured-icon">
      <i class="icon bi bi-shield-check fs-1"></i>
      <div class="info">
        <h4>Administrators</h4>
        <p><b><?php echo e($users->where('is_admin', true)->count()); ?></b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small success coloured-icon">
      <i class="icon bi bi-person fs-1"></i>
      <div class="info">
        <h4>Regular Users</h4>
        <p><b><?php echo e($users->where('is_admin', false)->count()); ?></b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small info coloured-icon">
      <i class="icon bi bi-file-text fs-1"></i>
      <div class="info">
        <h4>Total Posts</h4>
        <p><b><?php echo e($users->sum('posts_count')); ?></b></p>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/users/index.blade.php ENDPATH**/ ?>