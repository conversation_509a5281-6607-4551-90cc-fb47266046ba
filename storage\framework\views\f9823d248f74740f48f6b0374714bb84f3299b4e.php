
<?php $__env->startSection('title', 'Home'); ?>

<?php $__env->startSection('content'); ?>
    <div class="main-banner header-text">
      <div class="container-fluid">
        <div class="owl-banner owl-carousel">
          <?php $__empty_1 = true; $__currentLoopData = $featured_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
          <div class="item">
            <?php if($post->featured_image): ?>
              <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>">
            <?php else: ?>
              <img src="<?php echo e(asset('assets/images/banner-item-0' . (($loop->index % 6) + 1) . '.jpg')); ?>" alt="<?php echo e($post->title); ?>">
            <?php endif; ?>
            <div class="item-content">
              <div class="main-content">
                <div class="meta-category">
                  <span><?php echo e($post->category->name); ?></span>
                </div>
                <a href="<?php echo e(route('post-detail', $post->slug)); ?>"><h4><?php echo e($post->title); ?></h4></a>
                <ul class="post-info">
                  <li><a href="#"><?php echo e($post->user->name); ?></a></li>
                  <li><a href="#"><?php echo e($post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y')); ?></a></li>
                  <li><a href="#"><?php echo e($post->views_count); ?> Views</a></li>
                </ul>
              </div>
            </div>
          </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
          <div class="item">
            <img src="<?php echo e(asset('assets/images/banner-item-01.jpg')); ?>" alt="No Posts">
            <div class="item-content">
              <div class="main-content">
                <div class="meta-category">
                  <span>Blog</span>
                </div>
                <a href="#"><h4>No Posts Available</h4></a>
                <ul class="post-info">
                  <li><a href="#">Admin</a></li>
                  <li><a href="#"><?php echo e(date('M d, Y')); ?></a></li>
                  <li><a href="#">0 Views</a></li>
                </ul>
              </div>
            </div>
          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a rel="nofollow" href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $featured_posts->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-12">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      <?php if($post->featured_image): ?>
                        <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>">
                      <?php else: ?>
                        <img src="<?php echo e(asset('assets/images/blog-post-0' . (($loop->index % 3) + 1) . '.jpg')); ?>" alt="<?php echo e($post->title); ?>">
                      <?php endif; ?>
                    </div>
                    <div class="down-content">
                      <span><?php echo e($post->category->name); ?></span>
                      <a href="<?php echo e(route('post-detail', $post->slug)); ?>"><h4><?php echo e($post->title); ?></h4></a>
                      <ul class="post-info">
                        <li><a href="#"><?php echo e($post->user->name); ?></a></li>
                        <li><a href="#"><?php echo e($post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y')); ?></a></li>
                        <li><a href="#"><?php echo e($post->views_count); ?> Views</a></li>
                      </ul>
                      <p><?php echo e($post->excerpt ?: Str::limit(strip_tags($post->content), 200)); ?></p>
                      <div class="post-options">
                        <div class="row">
                          <div class="col-6">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              <?php $__currentLoopData = $post->tags->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"><?php echo e($tag->name); ?></a><?php if(!$loop->last): ?>,<?php endif; ?></li>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                          </div>
                          <div class="col-6">
                            <ul class="post-share">
                              <li><i class="fa fa-share-alt"></i></li>
                              <li><a href="#">Facebook</a>,</li>
                              <li><a href="#">Twitter</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-lg-12">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      <img src="<?php echo e(asset('assets/images/blog-post-01.jpg')); ?>" alt="No Posts">
                    </div>
                    <div class="down-content">
                      <span>Blog</span>
                      <a href="#"><h4>No Posts Available</h4></a>
                      <ul class="post-info">
                        <li><a href="#">Admin</a></li>
                        <li><a href="#"><?php echo e(date('M d, Y')); ?></a></li>
                        <li><a href="#">0 Views</a></li>
                      </ul>
                      <p>There are no blog posts available at the moment. Please check back later for new content.</p>
                    </div>
                  </div>
                </div>
                <?php endif; ?>
                <div class="col-lg-12">
                  <div class="main-button">
                    <a href="<?php echo e(route('blog')); ?>">View All Posts</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="<?php echo e(route('blog')); ?>" style="display: none;">
                      <input type="text" name="search" class="searchText" placeholder="type to search..." autocomplete="on">
                    </form>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $featured_posts->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recent_post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('post-detail', $recent_post->slug)); ?>">
                          <h5><?php echo e(Str::limit($recent_post->title, 50)); ?></h5>
                          <span><?php echo e($recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y')); ?></span>
                        </a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span><?php echo e(date('M d, Y')); ?></span>
                        </a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('blog', ['category' => $category->slug])); ?>">- <?php echo e($category->name); ?> (<?php echo e($category->posts_count); ?>)</a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">- No categories available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"><?php echo e($tag->name); ?></a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">No tags available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/website/index.blade.php ENDPATH**/ ?>