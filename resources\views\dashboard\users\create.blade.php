@extends('theme.layout.master')

@section('title', 'Create New User')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-person-plus"></i> Create New User</h1>
    <p>Add a new user to the system</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
    <li class="breadcrumb-item active">Create User</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">User Information</h3>
        
        <form action="{{ route('users.store') }}" method="POST" class="needs-validation" enctype="multipart/form-data" novalidate>
          @csrf

          <!-- Profile Image Upload -->
          <div class="row">
            <div class="col-md-12">
              <div class="mb-4">
                <label class="form-label">Profile Image</label>
                <div class="d-flex align-items-center">
                  <div class="me-3">
                    <img src="https://ui-avatars.com/api/?name=User&background=007bff&color=fff&size=80"
                         alt="Profile Preview"
                         class="rounded-circle"
                         style="width: 80px; height: 80px; object-fit: cover;"
                         id="image-preview">
                  </div>
                  <div class="flex-grow-1">
                    <input type="file" class="form-control @error('profile_image') is-invalid @enderror"
                           id="profile_image" name="profile_image" accept="image/*">
                    @error('profile_image')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                      Upload JPG, PNG, GIF images. Maximum size: 2MB
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" required>
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="mb-3">
                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email') }}" required>
                @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                       id="password" name="password" required>
                @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Minimum 8 characters required</div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="mb-3">
                <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" required>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <div class="form-check">
                  <input type="hidden" name="is_admin" value="0">
                  <input type="checkbox" class="form-check-input @error('is_admin') is-invalid @enderror" 
                         id="is_admin" name="is_admin" value="1" 
                         {{ old('is_admin') ? 'checked' : '' }}>
                  <label class="form-check-label" for="is_admin">
                    Administrator Privileges
                  </label>
                  @error('is_admin')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">Administrators have full access to the system</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="tile-footer">
            <div class="d-flex justify-content-between">
              <a href="{{ route('users.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Users
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="bi bi-arrow-clockwise"></i> Reset
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle"></i> Create User
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <div class="col-md-4">
    <div class="tile">
      <div class="tile-body">
        <h3 class="tile-title">User Guidelines</h3>
        
        <div class="alert alert-info">
          <h6><i class="bi bi-info-circle"></i> Creating a New User</h6>
          <ul class="mb-0 small">
            <li>All fields marked with <span class="text-danger">*</span> are required</li>
            <li>Email addresses must be unique in the system</li>
            <li>Passwords must be at least 8 characters long</li>
            <li>Administrator privileges grant full system access</li>
          </ul>
        </div>
        
        <div class="alert alert-warning">
          <h6><i class="bi bi-exclamation-triangle"></i> Security Notes</h6>
          <ul class="mb-0 small">
            <li>Use strong passwords with mixed characters</li>
            <li>Only grant admin privileges when necessary</li>
            <li>Users will need to verify their email address</li>
            <li>Consider sending login credentials securely</li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- User Statistics -->
    <div class="tile mt-3">
      <div class="tile-body">
        <h3 class="tile-title">Current Statistics</h3>
        
        <div class="row text-center">
          <div class="col-6">
            <div class="widget-small primary coloured-icon">
              <i class="icon bi bi-people fs-1"></i>
              <div class="info">
                <h4>Total Users</h4>
                <p><b>{{ \App\Models\User::count() }}</b></p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="widget-small danger coloured-icon">
              <i class="icon bi bi-shield-check fs-1"></i>
              <div class="info">
                <h4>Admins</h4>
                <p><b>{{ \App\Models\User::where('is_admin', true)->count() }}</b></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="tile mt-3">
      <div class="tile-body">
        <h3 class="tile-title">Quick Actions</h3>
        
        <div class="d-grid gap-2">
          <a href="{{ route('users.index') }}" class="btn btn-outline-primary">
            <i class="bi bi-list"></i> View All Users
          </a>
          <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
            <i class="bi bi-speedometer2"></i> Back to Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        // Check password confirmation
        const password = document.getElementById('password').value;
        const passwordConfirm = document.getElementById('password_confirmation').value;
        
        if (password !== passwordConfirm) {
            event.preventDefault();
            document.getElementById('password_confirmation').setCustomValidity('Passwords do not match');
        } else {
            document.getElementById('password_confirmation').setCustomValidity('');
        }
        
        form.classList.add('was-validated');
    });
    
    // Real-time password confirmation check
    document.getElementById('password_confirmation').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const passwordConfirm = this.value;
        
        if (password && passwordConfirm && password !== passwordConfirm) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
    
    // Password strength indicator (optional enhancement)
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        let strength = 0;

        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        // You can add visual strength indicator here if needed
        console.log('Password strength:', strength);
    });

    // Image preview functionality
    document.getElementById('profile_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('image-preview').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
