@extends('layouts.master')

@section('title', 'Dashboard')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-speedometer"></i> Dashboard</h1>
    <p>A free and open source Bootstrap 5 admin template</p>
  </div>
</div>

<div class="row">
  <div class="col-md-6 col-lg-3">
    <div class="widget-small primary coloured-icon"><i class="icon bi bi-people fs-1"></i>
      <div class="info"><h4>Users</h4><p><b>5</b></p></div>
    </div>
  </div>
  <!-- Add other widgets similarly -->
</div>

<div class="row">
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Weekly Sales - Last week</h3>
      <div class="ratio ratio-16x9"><div id="salesChart"></div></div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="tile">
      <h3 class="tile-title">Support Requests</h3>
      <div class="ratio ratio-16x9"><div id="supportRequestChart"></div></div>
    </div>
  </div>
</div>
@endsection

@push('scripts')
<script>
  const salesData = {
    xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    yAxis: { type: 'value', axisLabel: { formatter: '${value}' } },
    series: [{ data: [150, 230, 224, 218, 135, 147, 260], type: 'line', smooth: true }],
    tooltip: { trigger: 'axis', formatter: "<b>{b0}:</b> ${c0}" }
  };

  const supportRequests = {
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', left: 'left' },
    series: [{
      name: 'Support Requests', type: 'pie', radius: '50%',
      data: [
        { value: 300, name: 'In Progress' },
        { value: 50, name: 'Delayed' },
        { value: 100, name: 'Complete' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  const salesChart = echarts.init(document.getElementById('salesChart'), null, { renderer: 'svg' });
  salesChart.setOption(salesData);
  new ResizeObserver(() => salesChart.resize()).observe(document.getElementById('salesChart'));

  const supportChart = echarts.init(document.getElementById("supportRequestChart"), null, { renderer: 'svg' });
  supportChart.setOption(supportRequests);
  new ResizeObserver(() => supportChart.resize()).observe(document.getElementById("supportRequestChart"));
</script>
@endpush
