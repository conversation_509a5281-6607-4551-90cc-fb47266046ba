@extends('website.layouts.master')
@section('title', 'Home')

@section('content')
    <div class="main-banner header-text">
      <div class="container-fluid">
        <div class="owl-banner owl-carousel">
          @forelse($featured_posts as $post)
          <div class="item">
            @if($post->featured_image)
              <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}">
            @else
              <img src="{{ asset('assets/images/banner-item-0' . (($loop->index % 6) + 1) . '.jpg') }}" alt="{{ $post->title }}">
            @endif
            <div class="item-content">
              <div class="main-content">
                <div class="meta-category">
                  <span>{{ $post->category->name }}</span>
                </div>
                <a href="{{ route('post-detail', $post->slug) }}"><h4>{{ $post->title }}</h4></a>
                <ul class="post-info">
                  <li><a href="#">{{ $post->user->name }}</a></li>
                  <li><a href="#">{{ $post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y') }}</a></li>
                  <li><a href="#">{{ $post->views_count }} Views</a></li>
                </ul>
              </div>
            </div>
          </div>
          @empty
          <div class="item">
            <img src="{{ asset('assets/images/banner-item-01.jpg') }}" alt="No Posts">
            <div class="item-content">
              <div class="main-content">
                <div class="meta-category">
                  <span>Blog</span>
                </div>
                <a href="#"><h4>No Posts Available</h4></a>
                <ul class="post-info">
                  <li><a href="#">Admin</a></li>
                  <li><a href="#">{{ date('M d, Y') }}</a></li>
                  <li><a href="#">0 Views</a></li>
                </ul>
              </div>
            </div>
          </div>
          @endforelse
        </div>
      </div>
    </div>
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a rel="nofollow" href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                @forelse($featured_posts->take(3) as $post)
                <div class="col-lg-12">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      @if($post->featured_image)
                        <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}">
                      @else
                        <img src="{{ asset('assets/images/blog-post-0' . (($loop->index % 3) + 1) . '.jpg') }}" alt="{{ $post->title }}">
                      @endif
                    </div>
                    <div class="down-content">
                      <span>{{ $post->category->name }}</span>
                      <a href="{{ route('post-detail', $post->slug) }}"><h4>{{ $post->title }}</h4></a>
                      <ul class="post-info">
                        <li><a href="#">{{ $post->user->name }}</a></li>
                        <li><a href="#">{{ $post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y') }}</a></li>
                        <li><a href="#">{{ $post->views_count }} Views</a></li>
                      </ul>
                      <p>{{ $post->excerpt ?: Str::limit(strip_tags($post->content), 200) }}</p>
                      <div class="post-options">
                        <div class="row">
                          <div class="col-6">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              @foreach($post->tags->take(2) as $tag)
                                <li><a href="{{ route('blog', ['tag' => $tag->slug]) }}">{{ $tag->name }}</a>@if(!$loop->last),@endif</li>
                              @endforeach
                            </ul>
                          </div>
                          <div class="col-6">
                            <ul class="post-share">
                              <li><i class="fa fa-share-alt"></i></li>
                              <li><a href="#">Facebook</a>,</li>
                              <li><a href="#">Twitter</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                @empty
                <div class="col-lg-12">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      <img src="{{ asset('assets/images/blog-post-01.jpg') }}" alt="No Posts">
                    </div>
                    <div class="down-content">
                      <span>Blog</span>
                      <a href="#"><h4>No Posts Available</h4></a>
                      <ul class="post-info">
                        <li><a href="#">Admin</a></li>
                        <li><a href="#">{{ date('M d, Y') }}</a></li>
                        <li><a href="#">0 Views</a></li>
                      </ul>
                      <p>There are no blog posts available at the moment. Please check back later for new content.</p>
                    </div>
                  </div>
                </div>
                @endforelse
                <div class="col-lg-12">
                  <div class="main-button">
                    <a href="{{ route('blog') }}">View All Posts</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="{{ route('blog') }}" style="display: none;">
                      <input type="text" name="search" class="searchText" placeholder="type to search..." autocomplete="on">
                    </form>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($featured_posts->take(3) as $recent_post)
                        <li><a href="{{ route('post-detail', $recent_post->slug) }}">
                          <h5>{{ Str::limit($recent_post->title, 50) }}</h5>
                          <span>{{ $recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y') }}</span>
                        </a></li>
                        @empty
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span>{{ date('M d, Y') }}</span>
                        </a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($categories as $category)
                        <li><a href="{{ route('blog', ['category' => $category->slug]) }}">- {{ $category->name }} ({{ $category->posts_count }})</a></li>
                        @empty
                        <li><a href="#">- No categories available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($tags as $tag)
                        <li><a href="{{ route('blog', ['tag' => $tag->slug]) }}">{{ $tag->name }}</a></li>
                        @empty
                        <li><a href="#">No tags available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

@endsection
