<?php $__env->startSection('title', 'View Post'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-eye"></i> View Post</h1>
    <p><?php echo e($post->title); ?></p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('posts.index')); ?>">Posts</a></li>
    <li class="breadcrumb-item active">View</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="tile">
      <div class="tile-body">
        <!-- Post Header -->
        <div class="mb-4">
          <h1 class="display-6"><?php echo e($post->title); ?></h1>
          
          <div class="d-flex flex-wrap align-items-center text-muted mb-3">
            <span class="me-3">
              <i class="bi bi-person"></i> <?php echo e($post->user->name); ?>

            </span>
            <span class="me-3">
              <i class="bi bi-calendar"></i> 
              <?php if($post->published_at): ?>
                <?php echo e($post->published_at->format('M d, Y')); ?>

              <?php else: ?>
                Not published
              <?php endif; ?>
            </span>
            <span class="me-3">
              <i class="bi bi-eye"></i> <?php echo e($post->views_count); ?> views
            </span>
            <span class="me-3">
              <i class="bi bi-clock"></i> <?php echo e($post->reading_time); ?>

            </span>
          </div>

          <div class="mb-3">
            <span class="badge bg-secondary me-2"><?php echo e($post->category->name); ?></span>
            <?php if($post->status): ?>
              <span class="badge bg-success">Published</span>
            <?php else: ?>
              <span class="badge bg-warning">Draft</span>
            <?php endif; ?>
          </div>

          <?php if($post->tags->count() > 0): ?>
            <div class="mb-3">
              <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <span class="badge me-1" style="background-color: <?php echo e($tag->color); ?>">
                  <?php echo e($tag->name); ?>

                </span>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          <?php endif; ?>
        </div>

        <!-- Featured Image -->
        <?php if($post->featured_image): ?>
          <div class="mb-4">
            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                 alt="<?php echo e($post->title); ?>" 
                 class="img-fluid rounded">
          </div>
        <?php endif; ?>

        <!-- Excerpt -->
        <?php if($post->excerpt): ?>
          <div class="mb-4">
            <div class="alert alert-info">
              <strong>Excerpt:</strong> <?php echo e($post->excerpt); ?>

            </div>
          </div>
        <?php endif; ?>

        <!-- Content -->
        <div class="post-content">
          <?php echo nl2br(e($post->content)); ?>

        </div>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <!-- Actions -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Actions</h3>
      </div>
      <div class="tile-body">
        <div class="d-grid gap-2">
          <a href="<?php echo e(route('posts.edit', $post)); ?>" class="btn btn-warning">
            <i class="bi bi-pencil"></i> Edit Post
          </a>
          <a href="<?php echo e(route('posts.index')); ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Posts
          </a>
          <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" 
                onsubmit="return confirm('Are you sure you want to delete this post?')">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <button type="submit" class="btn btn-danger w-100">
              <i class="bi bi-trash"></i> Delete Post
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Post Information -->
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">Post Information</h3>
      </div>
      <div class="tile-body">
        <table class="table table-sm">
          <tr>
            <td><strong>Status:</strong></td>
            <td>
              <?php if($post->status): ?>
                <span class="badge bg-success">Published</span>
              <?php else: ?>
                <span class="badge bg-warning">Draft</span>
              <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td><strong>Category:</strong></td>
            <td><?php echo e($post->category->name); ?></td>
          </tr>
          <tr>
            <td><strong>Author:</strong></td>
            <td><?php echo e($post->user->name); ?></td>
          </tr>
          <tr>
            <td><strong>Views:</strong></td>
            <td><?php echo e($post->views_count); ?></td>
          </tr>
          <tr>
            <td><strong>Created:</strong></td>
            <td><?php echo e($post->created_at->format('M d, Y H:i')); ?></td>
          </tr>
          <tr>
            <td><strong>Updated:</strong></td>
            <td><?php echo e($post->updated_at->format('M d, Y H:i')); ?></td>
          </tr>
          <?php if($post->published_at): ?>
          <tr>
            <td><strong>Published:</strong></td>
            <td><?php echo e($post->published_at->format('M d, Y H:i')); ?></td>
          </tr>
          <?php endif; ?>
        </table>
      </div>
    </div>

    <!-- SEO Information -->
    <?php if($post->meta_title || $post->meta_description): ?>
    <div class="tile mb-3">
      <div class="tile-title-w-btn">
        <h3 class="title">SEO Information</h3>
      </div>
      <div class="tile-body">
        <?php if($post->meta_title): ?>
          <div class="mb-3">
            <strong>Meta Title:</strong>
            <p class="text-muted"><?php echo e($post->meta_title); ?></p>
          </div>
        <?php endif; ?>
        <?php if($post->meta_description): ?>
          <div class="mb-3">
            <strong>Meta Description:</strong>
            <p class="text-muted"><?php echo e($post->meta_description); ?></p>
          </div>
        <?php endif; ?>
      </div>
    </div>
    <?php endif; ?>

    <!-- Tags -->
    <?php if($post->tags->count() > 0): ?>
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">Tags</h3>
      </div>
      <div class="tile-body">
        <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <span class="badge me-1 mb-1" style="background-color: <?php echo e($tag->color); ?>">
            <?php echo e($tag->name); ?>

          </span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
    <?php endif; ?>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<style>
.post-content {
  line-height: 1.8;
  font-size: 1.1rem;
}

.post-content p {
  margin-bottom: 1.5rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/posts/show.blade.php ENDPATH**/ ?>