<?php $__env->startSection('title', 'Account Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-gear"></i> Account Settings</h1>
    <p>Manage your account preferences and security settings</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Settings</li>
  </ul>
</div>

<div class="row">
  <!-- Password Settings -->
  <div class="col-md-6">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-shield-lock"></i> Change Password
        </h3>
      </div>
      <div class="tile-body">
        <form action="<?php echo e(route('settings.password')); ?>" method="POST">
          <?php echo csrf_field(); ?>
          <?php echo method_field('PUT'); ?>
          
          <div class="mb-3">
            <label for="current_password" class="form-label">Current Password <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="password" class="form-control <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                     id="current_password" name="current_password" required>
              <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                <i class="bi bi-eye" id="current_password_icon"></i>
              </button>
            </div>
            <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
              <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>

          <div class="mb-3">
            <label for="password" class="form-label">New Password <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                     id="password" name="password" required>
              <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                <i class="bi bi-eye" id="password_icon"></i>
              </button>
            </div>
            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
              <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            <small class="form-text text-muted">Minimum 8 characters required</small>
          </div>

          <div class="mb-3">
            <label for="password_confirmation" class="form-label">Confirm New Password <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="password" class="form-control" 
                     id="password_confirmation" name="password_confirmation" required>
              <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                <i class="bi bi-eye" id="password_confirmation_icon"></i>
              </button>
            </div>
          </div>

          <div class="tile-footer">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-shield-check"></i> Update Password
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- General Settings -->
  <div class="col-md-6">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-sliders"></i> General Settings
        </h3>
      </div>
      <div class="tile-body">
        <form action="<?php echo e(route('settings.update')); ?>" method="POST">
          <?php echo csrf_field(); ?>
          <?php echo method_field('PUT'); ?>
          
          <div class="mb-3">
            <label for="site_name" class="form-label">Site Name</label>
            <input type="text" class="form-control <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                   id="site_name" name="site_name" value="<?php echo e(old('site_name', config('app.name'))); ?>">
            <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
              <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>

          <div class="mb-3">
            <label for="site_description" class="form-label">Site Description</label>
            <textarea class="form-control <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                      id="site_description" name="site_description" rows="3"><?php echo e(old('site_description', 'A modern blog management system')); ?></textarea>
            <?php $__errorArgs = ['site_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
              <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>

          <div class="mb-3">
            <label for="posts_per_page" class="form-label">Posts Per Page</label>
            <select class="form-select <?php $__errorArgs = ['posts_per_page'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                    id="posts_per_page" name="posts_per_page">
              <option value="5" <?php echo e(old('posts_per_page', 10) == 5 ? 'selected' : ''); ?>>5 posts</option>
              <option value="10" <?php echo e(old('posts_per_page', 10) == 10 ? 'selected' : ''); ?>>10 posts</option>
              <option value="15" <?php echo e(old('posts_per_page', 10) == 15 ? 'selected' : ''); ?>>15 posts</option>
              <option value="20" <?php echo e(old('posts_per_page', 10) == 20 ? 'selected' : ''); ?>>20 posts</option>
              <option value="25" <?php echo e(old('posts_per_page', 10) == 25 ? 'selected' : ''); ?>>25 posts</option>
            </select>
            <?php $__errorArgs = ['posts_per_page'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
              <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>

          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="comments_enabled" 
                     name="comments_enabled" value="1" <?php echo e(old('comments_enabled', true) ? 'checked' : ''); ?>>
              <label class="form-check-label" for="comments_enabled">
                Enable Comments
              </label>
            </div>
          </div>

          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="email_notifications" 
                     name="email_notifications" value="1" <?php echo e(old('email_notifications', true) ? 'checked' : ''); ?>>
              <label class="form-check-label" for="email_notifications">
                Email Notifications
              </label>
            </div>
          </div>

          <div class="tile-footer">
            <button type="submit" class="btn btn-success">
              <i class="bi bi-check-lg"></i> Save Settings
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Account Information -->
  <div class="col-md-12 mt-4">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-info-circle"></i> Account Information
        </h3>
      </div>
      <div class="tile-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-card">
              <div class="info-icon">
                <i class="bi bi-person-circle text-primary"></i>
              </div>
              <div class="info-content">
                <h6>Account Type</h6>
                <p>
                  <?php if(auth()->user()->is_admin): ?>
                    <span class="badge bg-danger">Administrator</span>
                  <?php else: ?>
                    <span class="badge bg-primary">User</span>
                  <?php endif; ?>
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <div class="info-icon">
                <i class="bi bi-calendar-check text-success"></i>
              </div>
              <div class="info-content">
                <h6>Member Since</h6>
                <p><?php echo e(auth()->user()->created_at->format('M d, Y')); ?></p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <div class="info-icon">
                <i class="bi bi-clock-history text-warning"></i>
              </div>
              <div class="info-content">
                <h6>Last Login</h6>
                <p><?php echo e(now()->format('M d, Y')); ?></p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <div class="info-icon">
                <i class="bi bi-shield-check text-info"></i>
              </div>
              <div class="info-content">
                <h6>Account Status</h6>
                <p><span class="badge bg-success">Active</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Links -->
  <div class="col-md-12 mt-4">
    <div class="tile">
      <div class="tile-title-w-btn">
        <h3 class="title">
          <i class="bi bi-link-45deg"></i> Quick Links
        </h3>
      </div>
      <div class="tile-body">
        <div class="row">
          <div class="col-md-3">
            <a href="<?php echo e(route('profile.edit')); ?>" class="quick-link-card">
              <i class="bi bi-person-fill"></i>
              <span>Edit Profile</span>
            </a>
          </div>
          <div class="col-md-3">
            <a href="<?php echo e(route('posts.index')); ?>" class="quick-link-card">
              <i class="bi bi-file-text"></i>
              <span>Manage Posts</span>
            </a>
          </div>
          <div class="col-md-3">
            <a href="<?php echo e(route('comments.index')); ?>" class="quick-link-card">
              <i class="bi bi-chat-dots"></i>
              <span>Comments</span>
            </a>
          </div>
          <div class="col-md-3">
            <a href="<?php echo e(route('dashboard')); ?>" class="quick-link-card">
              <i class="bi bi-speedometer2"></i>
              <span>Dashboard</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.info-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.info-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.info-icon {
  font-size: 2rem;
  margin-right: 15px;
}

.info-content h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.info-content p {
  margin: 5px 0 0 0;
  color: #6c757d;
}

.quick-link-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.quick-link-card:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,123,255,0.3);
  color: white;
  text-decoration: none;
}

.quick-link-card i {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.quick-link-card span {
  font-weight: 600;
  font-size: 1.1rem;
}

.tile {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.tile:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

.form-control, .form-select {
  border-radius: 8px;
}

.input-group .btn {
  border-radius: 0 8px 8px 0;
}
</style>

<?php $__env->startPush('js'); ?>
<script>
  function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
      field.type = 'text';
      icon.classList.remove('bi-eye');
      icon.classList.add('bi-eye-slash');
    } else {
      field.type = 'password';
      icon.classList.remove('bi-eye-slash');
      icon.classList.add('bi-eye');
    }
  }

  // Password strength indicator
  document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    // You can add password strength indicator here
  });

  function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/settings/index.blade.php ENDPATH**/ ?>