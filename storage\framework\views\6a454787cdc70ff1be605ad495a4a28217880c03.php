<?php $__env->startSection('title', 'Posts Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-file-text"></i> Posts Management</h1>
    <p>Manage your blog posts</p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Posts</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="tile-title">All Posts</h3>
          <a href="<?php echo e(route('posts.create')); ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add New Post
          </a>
        </div>

        <!-- Search and Filter Section -->
        <form method="GET" action="<?php echo e(route('posts.index')); ?>" class="mb-4">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group dashboard-search-container">
                <input type="text" id="posts-search" class="form-control dashboard-search" placeholder="Search posts..." autocomplete="off">
                <input type="text" name="search" class="form-control" placeholder="Search posts..."
                       value="<?php echo e(request('search')); ?>" style="display: none;">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select name="category" class="form-control">
                  <option value="">All Categories</option>
                  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                      <?php echo e($category->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <select name="status" class="form-control">
                  <option value="">All Status</option>
                  <option value="1" <?php echo e(request('status') === '1' ? 'selected' : ''); ?>>Published</option>
                  <option value="0" <?php echo e(request('status') === '0' ? 'selected' : ''); ?>>Draft</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-search"></i> Search
              </button>
              <?php if(request()->hasAny(['search', 'category', 'status'])): ?>
                <a href="<?php echo e(route('posts.index')); ?>" class="btn btn-secondary ms-1">
                  <i class="bi bi-x-circle"></i> Clear
                </a>
              <?php endif; ?>
            </div>
          </div>
        </form>

        <?php if(session('success')): ?>
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        <?php endif; ?>

        <div class="table-responsive">
          <table class="table table-hover table-bordered" id="postsTable">
            <thead>
              <tr>
                <th>Title</th>
                <th>Category</th>
                <th>Author</th>
                <th>Status</th>
                <th>Published Date</th>
                <th>Views</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    <?php if($post->featured_image): ?>
                      <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" class="rounded me-2" style="width: 50px; height: 50px; object-fit: cover;">
                    <?php endif; ?>
                    <div>
                      <strong><?php echo e(Str::limit($post->title, 50)); ?></strong>
                      <?php if($post->excerpt): ?>
                        <br><small class="text-muted"><?php echo e(Str::limit($post->excerpt, 80)); ?></small>
                      <?php endif; ?>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="badge bg-secondary"><?php echo e($post->category->name); ?></span>
                </td>
                <td><?php echo e($post->user->name); ?></td>
                <td>
                  <?php if($post->status): ?>
                    <span class="badge bg-success">Published</span>
                  <?php else: ?>
                    <span class="badge bg-warning">Draft</span>
                  <?php endif; ?>
                </td>
                <td>
                  <?php if($post->published_at): ?>
                    <?php echo e($post->published_at->format('M d, Y')); ?>

                  <?php else: ?>
                    <span class="text-muted">Not published</span>
                  <?php endif; ?>
                </td>
                <td>
                  <span class="badge bg-info"><?php echo e($post->views_count); ?></span>
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a href="<?php echo e(route('posts.show', $post)); ?>" class="btn btn-sm btn-info" title="View">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="<?php echo e(route('posts.edit', $post)); ?>" class="btn btn-sm btn-warning" title="Edit">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                      <?php echo csrf_field(); ?>
                      <?php echo method_field('DELETE'); ?>
                      <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
              <tr>
                <td colspan="7" class="text-center py-4">
                  <div class="text-muted">
                    <i class="bi bi-file-text fs-1"></i>
                    <p class="mt-2">No posts found. <a href="<?php echo e(route('posts.create')); ?>">Create your first post</a></p>
                  </div>
                </td>
              </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>

        <?php if($posts->hasPages()): ?>
          <div class="d-flex justify-content-center mt-3">
            <?php echo e($posts->links()); ?>

          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
  // Initialize DataTable if needed
  // $('#postsTable').DataTable();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/posts/index.blade.php ENDPATH**/ ?>