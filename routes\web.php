<?php

use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileSettingsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CommentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', [WebsiteController::class, 'index'])->name('home');
Route::get('/contact-us', [WebsiteController::class, 'contactUs'])->name('contact-us');
Route::get('/about', [WebsiteController::class, 'about'])->name('about');
Route::get('/blog', [WebsiteController::class, 'blog'])->name('blog');
Route::get('/post/{slug}', [WebsiteController::class, 'postDetail'])->name('post-detail');
Route::post('/post/{slug}/comment', [WebsiteController::class, 'storeComment'])
    ->name('post.comment')
    ->middleware('comment.throttle');
Route::get('/contact', [WebsiteController::class, 'contact'])->name('contact');
Route::post('/contact', [WebsiteController::class, 'sendContactForm'])->name('contact.send');

// AJAX Search Routes
Route::get('/search/posts', [WebsiteController::class, 'searchPosts'])->name('search.posts');
Route::get('/search/users', [UserController::class, 'searchUsers'])->name('search.users');
Route::get('/search/dashboard-posts', [PostController::class, 'searchPosts'])->name('search.dashboard-posts');

// dashboard
Route::get('/dashboard', [ThemeController::class, 'home'])->middleware('auth')->name('dashboard');

// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {


    // Blog Management Routes
    Route::resource('posts', PostController::class);
    Route::resource('categories', CategoryController::class);
    Route::resource('tags', TagController::class);
    Route::resource('users', UserController::class);
    Route::delete('users/{user}/remove-image', [UserController::class, 'removeImage'])->name('users.remove-image');
    Route::resource('comments', CommentController::class)->only(['index', 'destroy']);
    Route::patch('comments/{comment}/approve', [CommentController::class, 'approve'])->name('comments.approve');
    Route::patch('comments/{comment}/reject', [CommentController::class, 'reject'])->name('comments.reject');
    Route::post('comments/{comment}/reply', [CommentController::class, 'reply'])->name('comments.reply');

    // Profile & Settings Routes
    Route::get('profile', [ProfileSettingsController::class, 'profile'])->name('profile.edit');
    Route::put('profile', [ProfileSettingsController::class, 'updateProfile'])->name('profile.update');
    Route::delete('profile/image', [ProfileSettingsController::class, 'removeProfileImage'])->name('profile.remove-image');
    Route::get('settings', [ProfileSettingsController::class, 'settings'])->name('settings.index');
    Route::put('settings/password', [ProfileSettingsController::class, 'updatePassword'])->name('settings.password');
    Route::put('settings', [ProfileSettingsController::class, 'updateSettings'])->name('settings.update');
});


require __DIR__.'/auth.php';
