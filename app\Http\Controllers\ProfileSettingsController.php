<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileSettingsController extends Controller
{
    public function profile()
    {
        $user = Auth::user();
        $stats = [
            'posts_count' => $user->posts()->count(),
            'published_posts' => $user->posts()->where('status', true)->count(),
            'draft_posts' => $user->posts()->where('status', false)->count(),
            'total_views' => $user->posts()->sum('views_count'),
        ];

        return view('dashboard.profile.index', compact('user', 'stats'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image if exists
            if ($user->profile_image) {
                Storage::disk('public')->delete($user->profile_image);
            }
            $data['profile_image'] = $request->file('profile_image')->store('users', 'public');
        }

        $user->update($data);

        return redirect()->route('profile.edit')->with('success', 'Profile updated successfully!');
    }

    public function removeProfileImage()
    {
        $user = Auth::user();

        if ($user->profile_image) {
            Storage::disk('public')->delete($user->profile_image);
            $user->update(['profile_image' => null]);
        }

        return redirect()->route('profile.edit')->with('success', 'Profile image removed successfully!');
    }

    public function settings()
    {
        return view('dashboard.settings.index');
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::min(8)],
        ]);

        $user = Auth::user();

        // Check current password
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()->route('settings.index')->with('success', 'Password updated successfully!');
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'posts_per_page' => 'required|integer|min:1|max:50',
            'comments_enabled' => 'boolean',
            'email_notifications' => 'boolean',
        ]);

        // Store settings in cache or database
        // For now, we'll just return success
        // You can implement a settings table or use cache

        return redirect()->route('settings.index')->with('success', 'Settings updated successfully!');
    }
}
