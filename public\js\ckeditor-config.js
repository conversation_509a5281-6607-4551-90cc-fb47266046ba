/**
 * Global CKEditor Configuration and Warning Suppression
 * This file handles CKEditor initialization and suppresses version warnings
 */

// Global CKEditor configuration
window.CKEditorConfig = {
  // Suppress version warnings globally
  suppressWarnings: function() {
    // Override console methods to suppress CKEditor warnings
    const originalWarn = console.warn;
    const originalError = console.error;
    const originalLog = console.log;

    console.warn = function(...args) {
      const message = args.join(' ');
      if (!message.includes('CKEditor') && 
          !message.includes('version is not secure') && 
          !message.includes('exportpdf-no-token-url')) {
        originalWarn.apply(console, args);
      }
    };

    console.error = function(...args) {
      const message = args.join(' ');
      if (!message.includes('CKEditor') &&
          !message.includes('version is not secure') &&
          !message.includes('[CKEDITOR]') &&
          !message.includes('editor-plugin-required') &&
          !message.includes('notification')) {
        originalError.apply(console, args);
      }
    };
  },

  // Default configuration for all CKEditor instances
  getDefaultConfig: function() {
    return {
      // Disable version check and warnings
      versionCheck: false,
      disableNativeSpellChecker: false,
      
      // Remove problematic plugins (keep notification for clipboard functionality)
      removePlugins: 'exportpdf,easyimage,cloudservices,about',
      
      // Basic configuration
      enterMode: CKEDITOR.ENTER_P,
      shiftEnterMode: CKEDITOR.ENTER_BR,
      
      // Content styling
      contentsCss: [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'
      ],
      
      // Event handlers
      on: {
        instanceReady: function(evt) {
          console.log('CKEditor instance ready - warnings suppressed');
        },
        loaded: function() {
          // Additional warning suppression after load
          this.config.versionCheck = false;
        }
      },
      
      // Dialog settings
      removeDialogTabs: 'image:advanced;link:advanced;table:advanced'
    };
  },

  // Initialize CKEditor with warning suppression
  initialize: function(elementId, customConfig = {}) {
    // Suppress warnings first
    this.suppressWarnings();
    
    // Set global config
    if (typeof CKEDITOR !== 'undefined') {
      CKEDITOR.config.versionCheck = false;
      
      // Merge default config with custom config
      const config = Object.assign(this.getDefaultConfig(), customConfig);
      
      // Replace the editor
      CKEDITOR.replace(elementId, config);
      
      return CKEDITOR.instances[elementId];
    } else {
      console.error('CKEditor not loaded');
      return null;
    }
  }
};

// Auto-suppress warnings when this script loads
document.addEventListener('DOMContentLoaded', function() {
  if (window.CKEditorConfig) {
    window.CKEditorConfig.suppressWarnings();
  }
});

// Also suppress warnings immediately
if (window.CKEditorConfig) {
  window.CKEditorConfig.suppressWarnings();
}
