/* Dashboard Custom Styles - Minimal, Non-interfering Enhancements Only */

/* User avatar enhancement */
.app-sidebar__user-avatar {
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 50px;
  height: 45px;
  object-fit: cover;
  border-radius: 50%;
}

/* Badge styling */
.badge {
  font-size: 0.75em;
  padding: 0.25em 0.5em;
}

/* Subtle hover effect for menu items - only when .menu-hover class is added by JS */
.app-menu__item.menu-hover {
  background-color: rgba(255,255,255,0.03);
}

/* Smooth transitions for interactive elements */
.app-menu__item {
  transition: background-color 0.2s ease;
}

.treeview-item {
  transition: background-color 0.2s ease;
}

/* Timeline styles for user profile */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #007bff;
}

.timeline-title {
  margin: 0 0 5px 0;
  font-weight: 600;
  color: #495057;
}

.timeline-text {
  margin: 0 0 5px 0;
  color: #6c757d;
}

/* User profile enhancements */
.widget-small.coloured-icon .icon {
  opacity: 0.8;
}

.widget-small:hover .icon {
  opacity: 1;
  transform: scale(1.1);
  transition: all 0.3s ease;
}

/* Profile image upload enhancements */
.profile-image-upload {
  position: relative;
  display: inline-block;
}

.profile-image-upload img {
  transition: opacity 0.3s ease;
}

.profile-image-upload:hover img {
  opacity: 0.8;
}

.profile-image-upload .upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 123, 255, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  cursor: pointer;
}

.profile-image-upload:hover .upload-overlay {
  opacity: 1;
}

/* File input styling */
.file-input-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.file-input-wrapper input[type=file] {
  position: absolute;
  left: -9999px;
}

.file-input-label {
  cursor: pointer;
  display: inline-block;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.file-input-label:hover {
  background: #0056b3;
}

/* Image preview animations */
.image-preview {
  transition: all 0.3s ease;
}

.image-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Card enhancements */
.tile {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.tile:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

/* Widget styling */
.widget-small {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.widget-small:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.widget-small .icon {
  font-size: 2.5rem;
}

/* Table enhancements */
.table {
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
}

.table tbody tr {
  transition: background-color 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(0,123,255,0.05);
}

/* Button enhancements */
.btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-group .btn {
  margin-right: 2px;
}

/* Form enhancements */
.form-control {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  transform: translateY(-1px);
}

.form-select {
  border-radius: 6px;
}

/* Alert enhancements */
.alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

/* Pagination styling */
.pagination {
  border-radius: 8px;
  overflow: hidden;
}

.page-link {
  border: none;
  transition: all 0.3s ease;
}

.page-link:hover {
  background-color: #007bff;
  color: white;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Chart container */
.chart-container {
  position: relative;
  height: 300px;
  margin: 20px 0;
}

/* Loading states */
.loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-menu__item {
    padding: 12px 15px;
  }
  
  .treeview-item {
    padding-left: 40px;
  }
  
  .widget-small .icon {
    font-size: 2rem;
  }
  
  .btn-group {
    display: flex;
    flex-direction: column;
  }
  
  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 2px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tile {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .table {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .table thead th {
    background-color: #4a5568;
    color: #e2e8f0;
  }
}

/* Print styles */
@media print {
  .app-sidebar,
  .app-header,
  .btn,
  .pagination {
    display: none !important;
  }
  
  .app-content {
    margin-left: 0 !important;
  }
  
  .tile {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
