<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color'
    ];

    // Relationship with posts (many-to-many)
    public function posts()
    {
        return $this->belongsToMany(Post::class, 'post_tags');
    }

    // Get posts count
    public function getPostsCountAttribute()
    {
        return $this->posts()->count();
    }
}
